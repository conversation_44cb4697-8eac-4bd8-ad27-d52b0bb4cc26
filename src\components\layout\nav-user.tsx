import { Link } from 'react-router-dom';
import { LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export function NavUser() {
  const { user, logout } = useAuth();
  
  // Returns initials for avatar fallback, prioritizing full name if available, else username.
  const getInitials = () => {
    if (user.fullName) {
      // If the user has a full name with at least two words, use the initials of the first and last word.
      const nameParts = user.fullName.trim().split(' ');
      if (nameParts.length >= 2) {
        return `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`.toUpperCase();
      } else if (nameParts.length === 1 && nameParts[0].length > 0) {
        return nameParts[0][0].toUpperCase();
      }
    }
    // If no full name, use the first letter of the username as the fallback.
    return user.username[0].toUpperCase();
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-2">
        <Link
          to="/settings?tab=profile"
          className="flex items-center gap-2 p-2 hover:bg-accent hover:text-accent-foreground rounded-md  border border-border/20"
        >
          <Avatar className="h-8 w-8 rounded-lg">
            {user.avatarUrl ? (
              <AvatarImage src={user.avatarUrl} alt={user.fullName || user.username} />
            ) : null}
            <AvatarFallback>{getInitials()}</AvatarFallback>
          </Avatar>
          
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="font-semibold truncate">
              {user.fullName || user.username}
            </span>
            {user.email && (
              <span className="text-xs text-muted-foreground truncate">
                {user.email}
              </span>
            )}
          </div>
        </Link>
        <Button
          variant="outline"
          size="sm"
          onClick={logout}
          className="w-full justify-start"
        >
          <LogOut className="mr-2 h-4 w-4" />
          Log Out
        </Button>
      </div>
    </div>
  );
} 
import { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Warehouse, Package, AlertTriangle, Clock, Plus, Home, Info, Check } from 'lucide-react';
import { useInventory } from '@/context/InventoryContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { isBefore, addDays, parseISO, format } from 'date-fns';
import { getExpiryStatusStyle } from "@/utils/itemUtils";
import { Table, TableBody, TableRow, TableCell, TableHead, TableHeader } from '@/components/ui/table';
import { FloatingActionButton } from '@/components/ui/FloatingActionButton';
import LocationColorSwatch from '@/components/items-table/LocationColorSwatch';
import { Progress } from "@/components/ui/progress";
import { Toolt<PERSON>, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';

const Dashboard = () => {
  const { items, locations } = useInventory();
  const navigate = useNavigate();
  const [progress, setProgress] = useState<number>(0);

  // Simplified threshold state initialization
  const [expiringThreshold] = useState(localStorage.getItem('expiring-threshold') ? parseInt(localStorage.getItem('expiring-threshold')!) : 30);
  const [lowStockThreshold] = useState(localStorage.getItem('low-stock-threshold') ? parseInt(localStorage.getItem('low-stock-threshold')!) : 3);

  const uniqueItems = items.length;
  const totalQuantity = useMemo(() => items.reduce((sum, item) => sum + item.quantity, 0), [items]);

  // Calculate inventory health score (0-100)
  const inventoryHealthScore = useMemo(() => {
    if (items.length === 0) return 100; // Perfect score if no items (nothing to worry about)

    const today = new Date();
    let expiredCount = 0;
    let expiringCount = 0;
    let lowStockCount = 0;
    let totalItemsWithExpiry = 0;
    let totalItemsWithStockWatch = 0;

    items.forEach(item => {
      if (item.expiryDate) {
        totalItemsWithExpiry++;
        const expiryDate = parseISO(item.expiryDate);
        if (isBefore(expiryDate, today)) {
          expiredCount++;
        } else if (isBefore(expiryDate, addDays(today, expiringThreshold))) {
          expiringCount++;
        }
      }

      if (item.watchStock) {
        totalItemsWithStockWatch++;
        if (item.quantity <= lowStockThreshold) {
          lowStockCount++;
        }
      }
    });

    // Calculate penalties
    const expiryPenalty = totalItemsWithExpiry > 0 ?
      ((expiredCount * 1.0) + (expiringCount * 0.5)) / totalItemsWithExpiry * 50 : 0;

    const stockPenalty = totalItemsWithStockWatch > 0 ?
      (lowStockCount / totalItemsWithStockWatch) * 50 : 0;

    // Final score (100 - penalties)
    return Math.max(0, Math.round(100 - expiryPenalty - stockPenalty));
  }, [items, expiringThreshold, lowStockThreshold]);

  // Optimized item filtering using useMemo
  const { expiredItems, expiringItems, topExpiringItems, lowStockItems, recentItems } = useMemo(() => {
    const today = new Date();
    const expiredItems = [];
    const expiringItems = [];
    const lowStockItems = [];
    const locationMap = new Map();

    // Initialize location stats
    locations.forEach(location => {
      locationMap.set(location.id, {
        id: location.id,
        name: location.name,
        color: location.color,
        itemCount: 0,
        totalQuantity: 0
      });
    });

    items.forEach(item => {
      // Update location stats
      if (locationMap.has(item.locationId)) {
        const locationStat = locationMap.get(item.locationId);
        locationStat.itemCount++;
        locationStat.totalQuantity += item.quantity;
      }

      if (item.expiryDate) {
        const expiryDate = parseISO(item.expiryDate);
        if (isBefore(expiryDate, today)) {
          expiredItems.push(item);
        } else if (isBefore(expiryDate, addDays(today, expiringThreshold))) {
          expiringItems.push(item);
        }
      }
      if (item.watchStock && item.quantity <= lowStockThreshold) {
        lowStockItems.push(item);
      }
    });

    // Sort items
    lowStockItems.sort((a, b) => a.quantity - b.quantity);

    // Sort expiring items by expiry date (closest first)
    expiringItems.sort((a, b) => {
      const dateA = a.expiryDate ? new Date(a.expiryDate).getTime() : 0;
      const dateB = b.expiryDate ? new Date(b.expiryDate).getTime() : 0;
      return dateA - dateB;
    });

    // Get recent items sorted by updatedAt
    const recentItems = [...items]
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 3);

    // Limit expiring items to 3 for display
    const topExpiringItems = expiringItems.slice(0, 3);

    // We don't need locationStats for this design

    return {
      expiredItems,
      expiringItems,
      topExpiringItems,
      lowStockItems,
      recentItems
    };
  }, [items, locations, expiringThreshold, lowStockThreshold]);

  // Animate progress bar on load
  useEffect(() => {
    const timer = setTimeout(() => setProgress(inventoryHealthScore), 500);
    return () => clearTimeout(timer);
  }, [inventoryHealthScore]);

  // Navigate to items page with pre-filter
  const handleViewExpiredItems = () => {
    navigate('/items', { state: { filter: 'expired' } });
  };

  const handleViewExpiringItems = () => {
    navigate('/items', { state: { filter: 'expiring' } });
  };

  const handleViewLowStockItems = () => {
    navigate('/items', { state: { filter: 'low-stock' } });
  };

  return (
    <div>
      <div className="space-y-4 animate-slide-in">
        {/* Top Section with Hero and Health Score */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          {/* Hero Section - Takes 3/4 of the width on large screens */}
          <div className="lg:col-span-3 relative overflow-hidden rounded-lg border border-border/60 p-4 shadow-sm">
            {/* Background image with fade effect */}
            <div className="absolute inset-0 z-0"
              style={{
                backgroundImage: `linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.85) 50%, rgba(255,255,255,0.95) 100%), url('/dashboard3.png')`,
                backgroundSize: 'cover', /* You can adjust this value to scale the image */
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left center',
                opacity: 0.1, /* Adjusted opacity for better visibility */
                pointerEvents: 'none'
              }}>
            </div>
            <div className="flex flex-col h-full justify-between relative z-10">
              <div>
                <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2 mb-1">
                  <Home className="h-6 w-6 text-primary" />
                  Inventory Dashboard
                </h1>
                <p className="text-sm text-muted-foreground pt-1">
                  Welcome to your inventory management system. Here's an overview of your current inventory status.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-2 mt-3">
                <Button size="sm" onClick={() => navigate('/add-item')}>
                  <Plus className="mr-1 h-3.5 w-3.5" />
                  Add New Item
                </Button>
                <Button size="sm" variant="outline" onClick={() => navigate('/add-location')}>
                  <Warehouse className="mr-1 h-3.5 w-3.5" />
                  Add Location
                </Button>
              </div>
            </div>
          </div>

          {/* Health Score Card - Takes 1/4 of the width on large screens */}
          <div className="lg:col-span-1">
            <div className="border border-slate-200 dark:border-slate-800 rounded-lg overflow-hidden bg-white dark:bg-slate-900 shadow-sm h-full relative">
              {/* Health Score Header */}
              <div className="py-2 px-3 border-b border-slate-100 dark:border-slate-800 relative">
                <h3 className="text-xs font-medium text-slate-700 dark:text-slate-300">Inventory Health</h3>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span tabIndex={0} className="absolute top-2 right-2 align-middle cursor-pointer" style={{ lineHeight: 0 }}>
                      <Info className="w-4 h-4 text-slate-400 hover:text-slate-600 dark:text-slate-500 dark:hover:text-slate-300" aria-label="How is health score calculated?" />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="left">
                    <span>
                      The health score is calculated out of 100. Penalties are applied for:
                      <ul className="list-disc ml-4 mt-1">
                        <li>Expired items (-1 per item, up to 50 points)</li>
                        <li>Items expiring soon (-0.5 per item, up to 50 points)</li>
                        <li>Low stock items (-1 per item, up to 50 points)</li>
                      </ul>
                      A higher score means your inventory is in better condition.
                    </span>
                  </TooltipContent>
                </Tooltip>
              </div>

              {/* Health Score Display */}
              <div className="text-center py-4 px-4">
                <div className="text-sm text-slate-500 dark:text-slate-400 mb-1">Health Score</div>
                <div className="text-5xl font-bold mb-2">{inventoryHealthScore}</div>
                <Progress
                  value={progress}
                  className={`h-2 w-full rounded-full ${inventoryHealthScore > 70 ? 'bg-green-100' : inventoryHealthScore > 40 ? 'bg-amber-100' : 'bg-red-100'}`}
                  style={{
                    '--progress-foreground': inventoryHealthScore > 70 ? 'rgb(22, 163, 74)' : inventoryHealthScore > 40 ? 'rgb(202, 138, 4)' : 'rgb(220, 38, 38)'
                  } as React.CSSProperties}
                />
              </div>

              {/* Stats grid */}
              <div className="grid grid-cols-3 divide-x divide-slate-100 dark:divide-slate-800 border-t border-slate-100 dark:border-slate-800">
                <div className="py-2 px-1 text-center">
                  <div className="text-sm font-medium">{uniqueItems}</div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">Items</div>
                </div>
                <div className="py-2 px-1 text-center">
                  <div className="text-sm font-medium">{locations.length}</div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">Locations</div>
                </div>
                <div className="py-2 px-1 text-center">
                  <div className="text-sm font-medium">{totalQuantity}</div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">Units</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Alert Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {/* Expired Items Card */}
          <div className="border border-border/60 rounded-lg p-4 relative overflow-hidden shadow-sm">
            <div className="flex items-start justify-between">
              <div>
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500 dark:text-red-400" />
                  <h3 className="font-semibold dark:text-slate-200">Expired Items</h3>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {expiredItems.length === 0
                    ? "No expired items found."
                    : `You have ${expiredItems.length} item${expiredItems.length !== 1 ? 's' : ''} that ${expiredItems.length !== 1 ? 'have' : 'has'} expired.`}
                </p>
              </div>
              {expiredItems.length > 0 && (
                <div className="bg-red-50 dark:bg-red-950/50 font-bold text-3xl size-14 flex items-center justify-center rounded-lg text-red-500 dark:text-red-400">
                  {expiredItems.length}
                </div>
              )}
            </div>
            {expiredItems.length > 0 && (
              <Button
                variant="outline"
                className="w-full mt-4"
                onClick={handleViewExpiredItems}
              >
                View Expired Items
              </Button>
            )}
          </div>

          {/* Expiring Soon Card */}
          <div className="border border-border/60 rounded-lg p-4 relative overflow-hidden shadow-sm">
            <div className="flex items-start justify-between">
              <div>
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-amber-500 dark:text-amber-400" />
                  <h3 className="font-semibold dark:text-slate-200">Expiring Soon</h3>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {expiringItems.length === 0
                    ? "No items expiring soon."
                    : `You have ${expiringItems.length} item${expiringItems.length !== 1 ? 's' : ''} expiring within ${expiringThreshold} days.`}
                </p>
              </div>
              {expiringItems.length > 0 && (
                <div className="bg-amber-50 dark:bg-amber-950/50 font-bold text-3xl size-14 flex items-center justify-center rounded-lg text-amber-500 dark:text-amber-400">
                  {expiringItems.length}
                </div>
              )}
            </div>
            {expiringItems.length > 0 && (
              <Button
                variant="outline"
                className="w-full mt-4"
                onClick={handleViewExpiringItems}
              >
                View Expiring Items
              </Button>
            )}
          </div>

          {/* Low Stock Card */}
          <div className="border border-border/60 rounded-lg p-4 relative overflow-hidden shadow-sm">
            <div className="flex items-start justify-between">
              <div>
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                  <h3 className="font-semibold dark:text-slate-200">Low Stock</h3>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {lowStockItems.length === 0
                    ? "All items are well-stocked."
                    : `You have ${lowStockItems.length} item${lowStockItems.length !== 1 ? 's' : ''} below minimum stock level.`}
                </p>
              </div>
              {lowStockItems.length > 0 && (
                <div className="bg-blue-50 dark:bg-blue-950/50 font-bold text-3xl size-14 flex items-center justify-center rounded-lg text-blue-500 dark:text-blue-400">
                  {lowStockItems.length}
                </div>
              )}
            </div>
            {lowStockItems.length > 0 && (
              <Button
                variant="outline"
                className="w-full mt-4"
                onClick={handleViewLowStockItems}
              >
                View Low Stock Items
              </Button>
            )}
          </div>
        </div>

        {/* Dashboard Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Recent Items Section */}
          <Card className="shadow-sm">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>Your latest inventory updates</CardDescription>
                </div>
                <Button variant="outline" onClick={() => navigate('/items')}>
                  View All Items
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {recentItems.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="h-12 w-12 mx-auto mb-3 opacity-20" />
                  <p>No items in your inventory yet</p>
                  <Button variant="link" onClick={() => navigate('/add-item')} className="mt-2">
                    Add your first item
                  </Button>
                </div>
              ) : (
                <div className="px-4">
                  <Table className="border border-slate-200 dark:border-slate-700 rounded-md overflow-hidden">
                    <TableHeader>
                      <TableRow>
                        <TableHead>Item</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead className="text-center">Date</TableHead>
                        <TableHead className="text-right">Quantity</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentItems.map(item => (
                        <TableRow key={item.id} className="hover:bg-muted/50">
                          <TableCell>
                            <Button
                              variant="link"
                              className="p-0 h-auto font-medium hover:text-primary text-left"
                              onClick={() => navigate(`/items/${item.id}`)}
                            >
                              {item.name}
                            </Button>
                            {item.description && (
                              <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                                {item.description}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="link"
                              className="p-0 h-auto text-sm hover:text-primary flex items-center gap-1.5"
                              onClick={() => navigate(`/locations/${item.locationId}`)}
                            >
                              <LocationColorSwatch color={locations.find(l => l.id === item.locationId)?.color} />
                              <span>{locations.find(l => l.id === item.locationId)?.name || 'N/A'}</span>
                            </Button>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex flex-col items-center">
                              <div className="text-sm font-medium">
                                {format(parseISO(item.updatedAt), 'MMM d, yyyy')}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {new Date(item.updatedAt).getTime() !== new Date(item.createdAt).getTime() ? 'Updated' : 'Created'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="text-sm font-medium">{item.quantity}</div>
                            <div className="text-xs text-muted-foreground">{item.unit}</div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Expiring Items Section */}
          <Card className="shadow-sm">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    Expiring Soon
                  </CardTitle>
                  <CardDescription>Items expiring within {expiringThreshold} days</CardDescription>
                </div>
                <Button variant="outline" onClick={handleViewExpiringItems}>
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {expiringItems.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                <Check className="h-12 w-12 mx-auto mb-3 opacity-20" />
                <p>No items expiring</p>
                <Button variant="link" onClick={() => navigate('/add-item')} className="mt-2">
                  Add new item
                </Button>
              </div>
              ) : (
                <div className="px-4">
                  <Table className="border border-slate-200 dark:border-slate-700 rounded-md overflow-hidden">
                    <TableHeader>
                      <TableRow>
                        <TableHead>Item</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead className="text-center">Expiry Date</TableHead>
                        <TableHead className="text-right">Quantity</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {topExpiringItems.map(item => (
                        <TableRow key={item.id} className="hover:bg-muted/50">
                          <TableCell>
                            <Button
                              variant="link"
                              className="p-0 h-auto font-medium hover:text-primary text-left"
                              onClick={() => navigate(`/items/${item.id}`)}
                            >
                              {item.name}
                            </Button>
                            {item.description && (
                              <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                                {item.description}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="link"
                              className="p-0 h-auto text-sm hover:text-primary flex items-center gap-1.5"
                              onClick={() => navigate(`/locations/${item.locationId}`)}
                            >
                              <LocationColorSwatch color={locations.find(l => l.id === item.locationId)?.color} />
                              <span>{locations.find(l => l.id === item.locationId)?.name || 'N/A'}</span>
                            </Button>
                          </TableCell>
                          <TableCell className="text-center">
                            {item.expiryDate && (
                              <div className={`text-sm ${getExpiryStatusStyle(item.expiryDate)}`}>
                                {format(parseISO(item.expiryDate), 'MMM d, yyyy')}
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="text-sm font-medium">{item.quantity}</div>
                            <div className="text-xs text-muted-foreground">{item.unit}</div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Mobile FAB */}
      <FloatingActionButton
        actions={[
          {
            icon: <Warehouse className="h-5 w-5" />,
            label: "New Location",
            onClick: () => navigate('/add-location')
          },
          {
            icon: <Package className="h-5 w-5" />,
            label: "New Item",
            onClick: () => navigate('/add-item')
          }
        ]}
      />
    </div>
  );
};

export default Dashboard;

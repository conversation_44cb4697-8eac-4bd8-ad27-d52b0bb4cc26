import { createContext, useState, useContext, useEffect, ReactNode, useCallback } from 'react';
import { User } from '@/types';
import * as authUtils from '@/utils/authUtils';
import { loadUsers } from '@/utils/storageUtils'; // Import loadUsers
// Toast notifications for auth are handled in LoginPage, not here.

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isPendingApproval: boolean;
  isPasswordReset: boolean;
  login: (username: string, password: string) => Promise<User | null | { isPending: boolean } | { isPasswordReset: boolean }>; // Return User object, null, or status flags
  logout: () => void;
  refreshUser: () => void; // Add function to refresh user data
  registerNewUser: (userData: { username: string, password: string, fullName?: string, email?: string }) => Promise<boolean>;
  requestPasswordReset: (email: string) => Promise<boolean>;
  resetPasswordWithToken: (token: string, newPassword: string) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPendingApproval, setIsPendingApproval] = useState(false);
  const [isPasswordReset, setIsPasswordReset] = useState(false);

  // Retrieves a user object from localStorage by userId. Used to persist sessions and refresh user data.
  const getUserById = useCallback((userId: string): User | null => {
    try {
      const users = loadUsers();
      return users.find(u => u.id === userId) || null;
    } catch (error) {
      console.error("Error loading user:", error);
      return null;
    }
  }, []);

  // Refreshes the current user data from storage, ensuring the session is up to date.
  const refreshUser = useCallback(() => {
    const storedUserId = sessionStorage.getItem('loggedInUserId');
    if (storedUserId && user) {
      const freshUserData = getUserById(storedUserId);
      if (freshUserData) {
        setUser(freshUserData);
      }
    }
  }, [user, getUserById]);

  // On initial load, check sessionStorage for a persisted login and restore user if possible.
  useEffect(() => {
    setIsLoading(true);
    try {
      const storedUserId = sessionStorage.getItem('loggedInUserId');
      if (storedUserId) {
        // If userId is found, try to load the full user details
        const loggedInUser = getUserById(storedUserId);
        if (loggedInUser) {
          setUser(loggedInUser); // Restore session
        } else {
          // User ID found but user doesn't exist in storage anymore? Clear session.
          sessionStorage.removeItem('loggedInUserId');
          setUser(null);
        }
      } else {
        setUser(null); // No stored session
      }
    } catch (error) {
        console.error("Error restoring session:", error);
        sessionStorage.removeItem('loggedInUserId'); // Clear potentially corrupted session data
        setUser(null);
    } finally {
        setIsLoading(false);
    }
  }, [getUserById]);

  const login = useCallback(async (username: string, password: string): Promise<User | null | { isPending: boolean } | { isPasswordReset: boolean }> => {
    // isLoading state and toast notifications are managed in LoginPage, not here.
    setIsPendingApproval(false); // Reset pending status
    setIsPasswordReset(false); // Reset password reset status
    try {
      const loggedInUser = await authUtils.login(username, password);
      if (loggedInUser) {
        // Check if the user is pending approval
        if ('isPending' in loggedInUser) {
          setUser(null); // Don't set the user as logged in
          setIsPendingApproval(true); // Set pending flag
          return loggedInUser; // Return the user with pending flag
        }

        // Check if the user has a password reset request
        if ('isPasswordReset' in loggedInUser) {
          setUser(null); // Don't set the user as logged in
          setIsPasswordReset(true); // Set password reset flag
          return loggedInUser; // Return the user with password reset flag
        }

        setUser(loggedInUser);
        sessionStorage.setItem('loggedInUserId', loggedInUser.id); // Persist user ID
        return loggedInUser; // Return user object on success
      } else {
        setUser(null); // Ensure user state is null on failure
        return null; // Return null on failure (invalid credentials)
      }
    } catch (error) {
      console.error("Login error:", error);
      setUser(null); // Ensure user state is null on error
      return null; // Return null on error
    }
  }, []);

  const logout = useCallback(() => {
    setUser(null);
    setIsPendingApproval(false);
    setIsPasswordReset(false);
    sessionStorage.removeItem('loggedInUserId'); // Clear persisted session state
    // Logout toast can be handled where logout is invoked if needed
  }, []);

  // Registers a new user in the system with provided credentials and optional profile details.
  const registerNewUser = useCallback(async (userData: {
    username: string,
    password: string,
    fullName?: string,
    email?: string
  }): Promise<boolean> => {
    return await authUtils.registerNewUser(userData);
  }, []);

  // Initiates a password reset request for the given email.
  const requestPasswordReset = useCallback(async (email: string): Promise<boolean> => {
    return await authUtils.requestPasswordReset(email);
  }, []);

  // Completes a password reset using a token and new password.
  const resetPasswordWithToken = useCallback(async (token: string, newPassword: string): Promise<boolean> => {
    return await authUtils.resetPasswordWithToken(token, newPassword);
  }, []);

  const contextValue: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    isPendingApproval,
    isPasswordReset,
    login,
    logout,
    refreshUser,
    registerNewUser,
    requestPasswordReset,
    resetPasswordWithToken
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
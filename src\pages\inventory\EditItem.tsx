import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Save, InfoIcon } from 'lucide-react';
import { useInventory } from '@/context/InventoryContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { formatWithTimezone } from "@/utils/dateUtils";
import { validateItemName, validateItemFields } from "@/utils/formUtils";
import { useFormValidation } from "@/hooks/useFormValidation";
import { Item } from "@/types";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import BackButton from '@/components/ui/BackButton';
import LocationColorSwatch from '@/components/items-table/LocationColorSwatch';

const EditItem = () => {
  const { itemId } = useParams<{ itemId: string }>();
  const { items, locations, updateItem } = useInventory();
  const navigate = useNavigate();

  const item = items.find(item => item.id === itemId);

  const [name, setName] = useState('');
  const [quantity, setQuantity] = useState('');
  const [unit, setUnit] = useState('');
  const [isCustomUnit, setIsCustomUnit] = useState(false);
  const [locationId, setLocationId] = useState('');
  const [description, setDescription] = useState('');
  const [expiryDate, setExpiryDate] = useState<Date | null>(null);
  const [watchStock, setWatchStock] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const { error, validate, setError } = useFormValidation();

  // Extract all unique units from current items for the unit dropdown
  const existingUnits = useMemo(() =>
    Array.from(new Set(items.map(item => item.unit))).filter(Boolean),
    [items]
  );

  // Populate form fields with existing item data on mount
  useEffect(() => {
    if (item) {
      setName(item.name);
      setQuantity(item.quantity.toString());
      setUnit(item.unit);
      setLocationId(item.locationId);
      setDescription(item.description || '');
      setExpiryDate(item.expiryDate ? new Date(item.expiryDate) : null);
      setWatchStock(item.watchStock);

      // Check if the item's unit is custom (not in the list of existing units)
      const isUnitCustom = !existingUnits.includes(item.unit);
      setIsCustomUnit(isUnitCustom);
    } else {
      navigate('/items');
    }
  }, [item, navigate, existingUnits]);

  // Memoized handlers for form fields
  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
    validate(() => validateItemName(newName, items, locationId, itemId));
  }, [items, locationId, itemId, validate]);

  const handleLocationChange = useCallback((newLocationId: string) => {
    setLocationId(newLocationId);
    validate(() => validateItemName(name, items, newLocationId, itemId));
  }, [name, items, itemId, validate]);

  const handleQuantityChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setQuantity(e.target.value);
  }, []);

  const handleUnitSelection = useCallback((value: string) => {
    if (value === 'custom') {
      setIsCustomUnit(true);
      setUnit('');
    } else {
      setIsCustomUnit(false);
      setUnit(value);
    }
  }, []);

  const handleCustomUnitChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setUnit(e.target.value);
  }, []);

  const handleDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDescription(e.target.value);
  }, []);

  const handleExpiryDateSelect = useCallback((date: Date | null) => {
    setExpiryDate(date);
  }, []);

  const handleWatchStockChange = useCallback((checked: boolean) => {
    setWatchStock(checked);
  }, []);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    // Clear field errors
    setFieldErrors({});

    const updatedItem: Partial<Item> = {
      name: name.trim(),
      quantity: Number(quantity),
      unit: unit === 'custom' ? '' : unit.trim(),
      locationId,
      description: description.trim() || '',
      expiryDate: expiryDate ? format(expiryDate, 'yyyy-MM-dd') : null,
      watchStock,
    };

    // First validate name uniqueness
    const nameValidation = validate(() => validateItemName(name, items, locationId, itemId));
    if (!nameValidation) return;

    // Then validate all other fields
    const fieldsValidation = validateItemFields(updatedItem);

    if (!fieldsValidation.isValid) {
      // Handle specific field errors
      if (fieldsValidation.error === "Unit is required") {
        setFieldErrors({ unit: fieldsValidation.error });
        setError(''); // Clear the general error
      } else if (fieldsValidation.error === "Location is required") {
        setFieldErrors({ location: fieldsValidation.error });
        setError(''); // Clear the general error
      } else {
        // For other errors, use the general error display
        setError(fieldsValidation.error);
      }
      return;
    }

    try {
      updateItem(itemId, updatedItem);
      navigate(-1);
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      }
    }
  }, [
    itemId, name, quantity, unit, locationId, description, expiryDate, watchStock,
    items, validate, setError, updateItem, navigate
  ]);

  // Memoize computed values
  const formattedExpiryDate = useMemo(() =>
    expiryDate ? formatWithTimezone(expiryDate.toISOString(), 'PPP') : undefined
    , [expiryDate]);

  if (!item) return null;

  return (
    // Layout wrapper removed
    <div className="space-y-6 animate-slide-in">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">Edit Item</h1>
          <p className="text-muted-foreground">
            Update the details of this inventory item.
          </p>
        </div>
      </div>

      <Card className="max-w-2xl pt-2 mr-auto">
        <CardHeader>
          <CardTitle className="py-1 text-lg">Item Information</CardTitle>
          <CardDescription>
            Update the details of this inventory item.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="flex flex-col gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Item Name</Label>
              <Input
                id="name"
                type="text"
                placeholder="Enter item name"
                value={name}
                onChange={handleNameChange}
                className={error ? 'border-destructive' : ''}
                required
              />
              {error && (
                <p className="text-sm text-destructive">{error}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="Enter quantity"
                  value={quantity}
                  onChange={handleQuantityChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="unit">Unit</Label>
                {!isCustomUnit ? (
                  <Select value={unit} onValueChange={handleUnitSelection}>
                    <SelectTrigger
                      id="unit"
                      className={`w-full ${fieldErrors.unit ? 'border-destructive' : ''}`}
                    >
                      <SelectValue placeholder="Select unit" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[300px]">
                      <SelectItem value="custom">Custom</SelectItem>
                      {existingUnits.map(existingUnit => (
                        <SelectItem key={existingUnit} value={existingUnit}>
                          {existingUnit}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="flex gap-2">
                    <Input
                      id="unit"
                      placeholder="Enter custom unit"
                      value={unit}
                      onChange={handleCustomUnitChange}
                      className={fieldErrors.unit ? 'border-destructive' : ''}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsCustomUnit(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                )}
                {fieldErrors.unit && (
                  <p className="text-sm text-destructive">{fieldErrors.unit}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Select value={locationId} onValueChange={handleLocationChange} required>
                <SelectTrigger
                  id="location"
                  className={`w-full ${fieldErrors.location ? 'border-destructive' : ''}`}
                >
                  <SelectValue placeholder="Select a location" />
                </SelectTrigger>
                <SelectContent className="max-h-[300px]">
                  {locations.map(location => (
                    <SelectItem key={location.id} value={location.id}>
                      <span className="flex items-center gap-2">
                        <LocationColorSwatch color={location.color} />
                        {location.name}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {fieldErrors.location && (
                <p className="text-sm text-destructive">{fieldErrors.location}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="expiryDate">Expiry Date (Optional)</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="expiryDate"
                    variant="outline"
                    className="w-full justify-start px-2 font-normal"
                  >
                    <CalendarIcon className="text-muted-foreground" />
                    <span className={!formattedExpiryDate ? "text-muted-foreground" : ""}>
                      {formattedExpiryDate || "Select date..."}
                    </span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={expiryDate || undefined}
                    onSelect={handleExpiryDateSelect}
                    initialFocus
                    className="rounded-md border shadow-sm"
                  />
                </PopoverContent>
              </Popover>
              {expiryDate && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="mt-1"
                  onClick={() => setExpiryDate(null)}
                >
                  Clear date
                </Button>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Notes (Optional)</Label>
              <Textarea
                id="description"
                placeholder="Add description here..."
                value={description}
                onChange={handleDescriptionChange}
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2 -mt-3">
              <Checkbox
                id="watchStock"
                checked={watchStock}
                onCheckedChange={handleWatchStockChange}
              />
              <div className="grid gap-1.5 leading-none">
                <div className="flex items-center gap-2">
                  <label
                    htmlFor="watchStock"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Watch stock level
                  </label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="link" size="icon">
                          <InfoIcon />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Get notified when stock is running low</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate(-1)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={!!error || !name.trim()}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      <div className="flex gap-2">
        <BackButton />
      </div>
    </div>
    // Layout wrapper removed
  );
};

export default EditItem;

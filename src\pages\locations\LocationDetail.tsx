import { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Search, Package, Edit, Plus, Trash2, MoveRight, ListPlus, FileDown, X, MapPin, Calendar, Clock, Boxes, BarChart3 } from 'lucide-react';
import { useInventory } from '@/context/InventoryContext';
import { loadUsers } from '@/utils/storageUtils';
import { User } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { parseISO, isBefore, addDays } from "date-fns";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "sonner";
import { formatWithTimezone } from "@/utils/dateUtils";
import DataTableFilter from '@/components/items-table/DataTableFilter';
import { Skeleton } from "@/components/ui/skeleton";
import DataTable from '@/components/items-table/DataTable';
import { FloatingActionButton } from '@/components/ui/FloatingActionButton';
import ShowLimitSelect from '@/components/items-table/ShowLimitSelect';

const EXPIRY_FILTER_OPTIONS = [
  { label: 'Clear filter', value: 'none' },
  { label: 'Already expired', value: 'expired' },
  { label: '30 days', value: '30' },
  { label: '60 days', value: '60' },
  { label: '90 days', value: '90' },
];

const LocationDetail = () => {
  const { locationId } = useParams<{ locationId: string }>();
  const { getLocationById, getItemsInLocation, deleteItem, updateItem, locations, isLoading } = useInventory();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<'name' | 'quantity' | 'updatedAt' | 'expiryDate'>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showMoveDialog, setShowMoveDialog] = useState(false);
  const [targetLocationId, setTargetLocationId] = useState<string>("");
  const [itemToDelete, setItemToDelete] = useState<string | null>(null);
  const [showSingleDeleteConfirm, setShowSingleDeleteConfirm] = useState(false);
  const [expiryFilter, setExpiryFilter] = useState<string>('none');

  const navigate = useNavigate();
  const [users, setUsers] = useState<User[]>([]);

  // Load user data on mount
  useEffect(() => {
    setUsers(loadUsers());
  }, []);

  // Memoize user ID to username mapping
  const userMap = useMemo(() => {
    const map = new Map<string, string>();
    users.forEach(user => map.set(user.id, user.username));
    return map;
  }, [users]);

  if (!locationId) {
    navigate(-1);
    return null;
  }

  const location = getLocationById(locationId);

  if (!location) {
    navigate(-1);
    return null;
  }

  const itemsInLocation = getItemsInLocation(locationId);
  const filteredItems = itemsInLocation.filter(item => {
    if (searchTerm && !item.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    if (expiryFilter !== 'none') {
      const today = new Date();
      const expiryDate = item.expiryDate ? parseISO(item.expiryDate) : null;

      if (!expiryDate) {
        return false;
      }

      if (expiryFilter === 'expired') {
        return isBefore(expiryDate, today);
      } else {
        const days = parseInt(expiryFilter);
        return isBefore(expiryDate, addDays(today, days)) &&
          !isBefore(expiryDate, today);
      }
    }

    return true;
  });

  const toggleSort = (field: 'name' | 'quantity' | 'updatedAt' | 'expiryDate') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedItems = [...filteredItems].sort((a, b) => {
    if (sortField === 'name') {
      return sortDirection === 'asc'
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    } else if (sortField === 'quantity') {
      return sortDirection === 'asc'
        ? a.quantity - b.quantity
        : b.quantity - a.quantity;
    } else if (sortField === 'expiryDate') {
      const dateA = a.expiryDate ? new Date(a.expiryDate).getTime() : 0;
      const dateB = b.expiryDate ? new Date(b.expiryDate).getTime() : 0;
      return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
    } else {
      return sortDirection === 'asc'
        ? new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
        : new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    }
  });

  // Pagination state and logic
  const totalPages = Math.ceil(sortedItems.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedItems = sortedItems.slice(startIndex, startIndex + itemsPerPage);

  const handleDelete = (itemId: string) => {
    setItemToDelete(itemId);
    setShowSingleDeleteConfirm(true);
  };

  const confirmSingleDelete = () => {
    if (itemToDelete) {
      deleteItem(itemToDelete);
      setItemToDelete(null);
      toast.success("Item Deleted", {
        description: "The item has been successfully deleted."
      });
    }
    setShowSingleDeleteConfirm(false);
  };

  const handleSelectAll = () => {
    if (selectedItems.size === paginatedItems.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(paginatedItems.map(item => item.id)));
    }
  };

  const handleSelectItem = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  const handleBatchDelete = () => {
    selectedItems.forEach(itemId => {
      deleteItem(itemId);
    });
    setSelectedItems(new Set());
    setShowDeleteConfirm(false);
    toast.success("Items Deleted", {
      description: `Successfully deleted ${selectedItems.size} items.`
    });
  };

  const handleBatchMove = () => {
    if (!targetLocationId) return;

    Array.from(selectedItems).forEach(itemId => {
      const item = itemsInLocation.find(i => i.id === itemId);
      if (item) {
        updateItem(itemId, { ...item, locationId: targetLocationId });
      }
    });

    setSelectedItems(new Set());
    setShowMoveDialog(false);
    setTargetLocationId("");
    toast.success("Items Moved", {
      description: `Successfully moved ${selectedItems.size} items to ${getLocationById(targetLocationId)?.name}.`
    });
  };

  return (
    <>
      <div className="space-y-6 animate-slide-in">
        {/* Header and Back Button */}
        <div>
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 rounded-md flex items-center justify-center shadow-sm"
                style={{ backgroundColor: location.color || '#888888' }}
              >
                <MapPin className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold tracking-tight">{location.name}</h1>
                <p className="text-muted-foreground">{location.description || 'No description provided'}</p>
              </div>
            </div>
            <div className="flex flex-col gap-2">
              {/* Mobile Edit Button */}
              <Button
                variant="outline"
                onClick={() => navigate(`/locations/edit/${locationId}`)}
                className="md:hidden"
                size="icon"
              >
                <Edit className="h-4 w-4" />
                <span className="sr-only">Edit Location</span>
              </Button>
              {/* Desktop Action Buttons */}
              <div className="hidden md:flex gap-2">
                <Button variant="outline" onClick={() => navigate(`/locations/edit/${locationId}`)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Location
                </Button>
                <Button variant="outline" onClick={() => navigate('/batch-add-items', { state: { locationId } })}>
                  <ListPlus className="mr-2 h-4 w-4" />
                  Batch Add
                </Button>
                <Button onClick={() => navigate('/add-item', { state: { locationId } })}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Item
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Location Summary Card */}
        <Card className="overflow-hidden py-0 gap-2 pb-1">
          <div className="px-4 py-2 border-b border-border flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div>
                <h3 className="font-medium">Location Summary</h3>
                <p className="text-xs text-muted-foreground">Overview of items in this location</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(`/locations/${locationId}/pdf`)}
              disabled={isLoading}
              className="h-8"
            >
              <FileDown className="mr-2 h-4 w-4" />
              Export PDF
            </Button>
          </div>

          <CardContent className="px-4 pb-1">
            {isLoading ? (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-sm text-muted-foreground mb-1">Unique Items</div>
                  <Skeleton className="h-8 w-16" />
                </div>
                <div>
                  <div className="text-sm text-muted-foreground mb-1">Total Quantity</div>
                  <Skeleton className="h-8 w-16" />
                </div>
                <div>
                  <div className="text-sm text-muted-foreground mb-1">Created</div>
                  <Skeleton className="h-8 w-24" />
                </div>
                <div>
                  <div className="text-sm text-muted-foreground mb-1">Updated</div>
                  <Skeleton className="h-8 w-24" />
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                    <Boxes className="h-3.5 w-3.5" /> Unique Items
                  </div>
                  <div className="text-1xl font-bold">{itemsInLocation.length}</div>
                </div>

                <div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                    <BarChart3 className="h-3.5 w-3.5" /> Total Quantity
                  </div>
                  <div className="text-1xl font-bold">{itemsInLocation.reduce((sum, item) => sum + item.quantity, 0)}</div>
                </div>

                <div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                    <Calendar className="h-3.5 w-3.5" /> Created {location.createdByUserId && (
                      <span className="ml-1">by {userMap.get(location.createdByUserId) || 'Unknown'}</span>
                    )}
                  </div>
                  <div className="text-sm font-medium">{formatWithTimezone(location.createdAt, 'PP', true)}</div>
                </div>

                <div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                    <Clock className="h-3.5 w-3.5" /> Updated {location.updatedByUserId && (
                      <span className="ml-1">by {userMap.get(location.updatedByUserId) || 'Unknown'}</span>
                    )}
                  </div>
                  <div className="text-sm font-medium">{formatWithTimezone(location.updatedAt, 'PP', true)}</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Filters Row */}
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4 w-full">
          <div className="relative flex-1 min-w-[180px]">
            <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground " />
            <Input
              type="text"
              placeholder="Search items in this location..."
              className="pl-8 pr-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              disabled={isLoading}
            />
            {searchTerm && (
              <Button variant="ghost" size="icon" className="absolute right-2 top-1/2 h-6 w-6 -translate-y-1/2 text-muted-foreground hover:text-foreground" onClick={() => setSearchTerm('')}>
                <X className="h-4 w-4" />
                <span className="sr-only">Clear search</span>
              </Button>
            )}
          </div>
          <div className="flex flex-row gap-2 w-full sm:w-auto">
            <DataTableFilter
              value={expiryFilter}
              onValueChange={setExpiryFilter}
              options={EXPIRY_FILTER_OPTIONS}
              placeholder="Expiring in..."
              className="w-[140px]"
              disabled={isLoading}
            />
            <ShowLimitSelect
              value={itemsPerPage}
              onChange={(value) => { setItemsPerPage(value); setCurrentPage(1); }}
              totalItems={sortedItems.length}
              disabled={isLoading}
              className=""
            />
          </div>
        </div>

        {/* Batch Actions */}
        {selectedItems.size > 0 && (
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2 mt-2">
            <span className="text-sm text-muted-foreground order-last sm:order-none">{selectedItems.size} selected</span>
            <Button variant="outline" size="sm" onClick={() => navigate(`/locations/${locationId}/batch-edit`, { state: { selectedItems: Array.from(selectedItems), locationName: location.name } })} className="w-full sm:w-auto">
              <Edit className="mr-2 h-4 w-4" /> Batch Edit ({selectedItems.size})
            </Button>
            <Button variant="outline" size="sm" onClick={() => setShowMoveDialog(true)} className="w-full sm:w-auto">
              <MoveRight className="mr-2 h-4 w-4" /> Move
            </Button>
            <Button variant="outline" size="sm" className="text-destructive hover:text-destructive w-full sm:w-auto" onClick={() => setShowDeleteConfirm(true)}>
              <Trash2 className="mr-2 h-4 w-4" /> Delete
            </Button>
          </div>
        )}

        {/* Data Table */}
        <DataTable
          items={itemsInLocation} // Provide the full list of items for context
          locations={locations}
          getLocationById={getLocationById}
          isLoading={isLoading}
          paginatedItems={paginatedItems} // Provide only paginated items for display
          sortField={sortField}
          sortDirection={sortDirection}
          itemsPerPage={itemsPerPage}
          currentPage={currentPage}
          totalPages={totalPages}
          startIndex={startIndex}
          sortedItems={sortedItems} // Provide sorted items for selection logic
          selectedItems={selectedItems}
          showLocationColumn={false} // Hide location column in this context
          emptyMessage={{
            title: "No items found",
            description: itemsInLocation.length === 0 ? "This location doesn't have any items yet." : "Try adjusting your search term.",
            buttonText: "Add First Item",
            buttonAction: () => navigate('/add-item', { state: { locationId } })
          }}
          onToggleSort={toggleSort}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(value) => { setItemsPerPage(value); setCurrentPage(1); }}
          onSelectItem={handleSelectItem}
          onSelectAll={handleSelectAll}
          onEdit={(id) => navigate(`/edit-item/${id}`)}
          onDelete={handleDelete}
        />
      </div>

      {/* Mobile FAB */}
      <FloatingActionButton
        actions={[
          { icon: <ListPlus className="h-5 w-5" />, label: "Batch Add", onClick: () => navigate('/batch-add-items', { state: { locationId } }) },
          { icon: <Package className="h-5 w-5" />, label: "New Item", onClick: () => navigate('/add-item', { state: { locationId } }) }
        ]}
      />

      {/* Batch Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete {selectedItems.size} selected items.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBatchDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Move Items Dialog */}
      <Dialog open={showMoveDialog} onOpenChange={setShowMoveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Move Items</DialogTitle>
            <DialogDescription>Select a location to move {selectedItems.size} items to.</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <Select value={targetLocationId} onValueChange={setTargetLocationId}>
              <SelectTrigger><SelectValue placeholder="Select a location" /></SelectTrigger>
              <SelectContent>
                {locations.filter(loc => loc.id !== locationId).map(location => (
                  <SelectItem key={location.id} value={location.id}>{location.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowMoveDialog(false)}>Cancel</Button>
            <Button onClick={handleBatchMove} disabled={!targetLocationId}>Move Items</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Single Item Delete Confirmation Dialog */}
      <AlertDialog open={showSingleDeleteConfirm} onOpenChange={setShowSingleDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this item.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmSingleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default LocationDetail;

// Cleanup script for build files
import { execSync } from 'child_process';
import fs from 'fs';

console.log('🧹 Starting cleanup of build files...');

try {
  // Clean directories to remove
  const directoriesToClean = [
    './dist',                           // Frontend build output
    './node_modules/.vite',             // Vite cache
  ];

  // Check and clean each directory
  for (const dir of directoriesToClean) {
    if (fs.existsSync(dir)) {
      console.log(`Removing ${dir}...`);
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`✅ Removed ${dir}`);
    } else {
      console.log(`Directory ${dir} does not exist, skipping.`);
    }
  }

  // Check if Tauri directories exist before cleaning
  const tauriDirectories = [
    './src-tauri/target',               // Rust build output
    './src-tauri/gen',                  // Tauri generated files
    './src-tauri/.cargo',               // Cargo cache
  ];

  for (const dir of tauriDirectories) {
    if (fs.existsSync(dir)) {
      console.log(`Removing ${dir}...`);
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`✅ Removed ${dir}`);
    } else {
      console.log(`Directory ${dir} does not exist, skipping.`);
    }
  }

  // Only run cargo clean if src-tauri directory exists
  if (fs.existsSync('./src-tauri')) {
    console.log('\n🦀 Running cargo clean in src-tauri directory...');
    try {
      // Try to get the cargo path
      let cargoPath = 'cargo';
      // Check if we're on Windows
      const isWindows = process.platform === 'win32';

      if (isWindows) {
        try {
          // On Windows, use where to find cargo
          cargoPath = execSync('where cargo', { encoding: 'utf8' }).trim().split('\r\n')[0];
        } catch (e) {
          console.log('Could not find cargo with "where" command, using default "cargo"');
        }
      } else {
        try {
          // On Unix-like systems, use which
          cargoPath = execSync('which cargo', { encoding: 'utf8' }).trim();
        } catch (e) {
          console.log('Could not find cargo with "which" command, using default "cargo"');
        }
      }

      // Use different command syntax based on platform
      if (isWindows) {
        // On Windows, change directory and then execute command separately
        process.chdir('./src-tauri');
        execSync(`"${cargoPath}" clean`, { stdio: 'inherit' });
        process.chdir('..');
      } else {
        // On Unix-like systems
        execSync(`cd src-tauri && "${cargoPath}" clean`, { stdio: 'inherit' });
      }
      console.log('✅ Cargo clean completed');
    } catch (cargoError) {
      console.log(`⚠️ Cargo clean failed: ${cargoError.message}`);
      console.log('Continuing with cleanup process...');
    }
  } else {
    console.log('\n⚠️ src-tauri directory not found, skipping cargo clean.');
  }

  // Clean npm cache if needed
  console.log('\n📦 Cleaning npm cache for the project...');
  execSync('npm cache clean --force', { stdio: 'inherit' });
  console.log('✅ npm cache cleaned');

  console.log('\n🎉 Cleanup completed successfully!');
  console.log('You can now run the production build with a clean environment.');
} catch (error) {
  console.error('\n❌ Error during cleanup:', error.message);
  process.exit(1);
}

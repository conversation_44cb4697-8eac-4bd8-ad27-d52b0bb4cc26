import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

const ProtectedRoute: React.FC = () => {
  const { isLoading, isAuthenticated } = useAuth();

  if (isLoading) {
    // Optional: Show a loading indicator while auth state is resolving
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  if (!isAuthenticated) {
    // Redirect unauthenticated users to the login page
    return <Navigate to="/login" replace />;
  }

  // If authenticated, render the child routes (the protected page)
  return <Outlet />;
};

export default ProtectedRoute;
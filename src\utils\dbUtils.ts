/**
 * Utility functions for handling database operations and migrations.
 */

/**
 * <PERSON>les IndexedDB migration errors by creating a mock database.
 * Prevents "Migrator X should migrate: false" errors.
 */
export const handleMigratorErrors = (): void => {
  // List of migrator versions to handle
  const migratorVersions = [
    { name: 'Rf', version: 53 },
    { name: 'Uf', version: 54 },
    { name: '$f', version: 55 },
    { name: 'Yf', version: 56 },
    { name: 'ey', version: 57 },
    { name: 'ny', version: 58 },
    { name: 'oy', version: 59 },
    { name: 'gy', version: 60 },
    { name: 'Ay', version: 61 },
    { name: 'By', version: 62 },
    { name: 'Ty', version: 63 },
    { name: 'Oy', version: 64 },
    { name: '<PERSON><PERSON>', version: 65 },
    { name: 'ev', version: 66 },
    { name: 'nv', version: 67 },
    { name: 'sv', version: 68 },
    { name: 'uv', version: 69 },
    { name: 'mv', version: 70 }
  ];

  // Create a mock database to handle the migrations
  const dbName = 'imbentory-mock-db';
  const dbVersion = 70; // Use the highest version

  // Open the database with the correct version
  const request = indexedDB.open(dbName, dbVersion);

  // Handle database upgrade event
  request.onupgradeneeded = (event) => {
    const req = event.target as IDBOpenDBRequest;
    const db = req.result;

    // Create a single object store to track migrations
    if (!db.objectStoreNames.contains('migrations')) {
      db.createObjectStore('migrations', { keyPath: 'name' });
    }

    // Add each migrator to the store if transaction exists
    const transaction = req.transaction;
    if (transaction) {
      const migrationStore = transaction.objectStore('migrations');
      migratorVersions.forEach(migrator => {
        migrationStore.add({
          name: migrator.name,
          version: migrator.version,
          migrated: true,
          timestamp: new Date().toISOString()
        });
      });
    }
  };


  // Log success or error
  request.onsuccess = () => {
    console.log('Mock migration database created successfully');
  };

  request.onerror = (event) => {
    const req = event.target as IDBOpenDBRequest;
    console.error('Error creating mock migration database:', req.error);
  };
};

/**
 * Connects to the service worker to initialize the database.
 * Ensures the service worker is active and handling migrations.
 */
export const connectToServiceWorker = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!('serviceWorker' in navigator)) {
      console.warn('Service workers are not supported in this browser');
      resolve();
      return;
    }

    navigator.serviceWorker.ready
      .then(registration => {
        // Create a message channel
        const messageChannel = new MessageChannel();
        
        // Set up the message handler
        messageChannel.port1.onmessage = (event) => {
          if (event.data.success) {
            console.log('Service worker database initialized');
            resolve();
          } else {
            console.error('Service worker database initialization failed:', event.data.error);
            reject(new Error(event.data.error));
          }
        };
        
        // Send the message to the service worker
        registration.active.postMessage(
          { type: 'INIT_DB' },
          [messageChannel.port2]
        );
      })
      .catch(error => {
        console.error('Error connecting to service worker:', error);
        reject(error);
      });
  });
};

import { Item, Location } from '@/types';
import { addDays, subDays, formatISO } from 'date-fns';

// Sample data for generating realistic test items
const SAMPLE_LOCATION_NAMES = [
  'Main Warehouse', 'Receiving Dock', 'Shipping Area', 'Front Office', 'IT Storage',
  'Maintenance Room', 'Cold Storage', 'Bulk Storage', 'Production Floor', 'Quality Control',
  'Showroom', 'Sales Floor', 'Back Room', 'Mezzanine', 'Basement',
  'Second Floor', 'Third Floor', 'Locker Room', 'Janitor Closet', 'Outdoor Shed',
  'Garage', 'Server Room', 'Archive Room', 'Security Office', 'Break Room',
  'Conference Room', 'Test Lab', 'R&D Lab', 'Packaging Area', 'Loading Bay',
  'Retail Space', 'Parts Room', 'Tool Crib', 'Sample Room', 'Returns Area',
  'Dispatch Zone', 'Inspection Room', 'Print Room', 'Studio', 'Workshop',
  'Storage Cage', 'Supply Closet', 'Chemical Storage', 'Medical Supply', 'Freezer'
];

const SAMPLE_ITEMS = [
  'Laptop', 'Mouse', 'Keyboard', 'Monitor', 'Headphones',
  'Desk', 'Chair', 'Printer', 'Scanner', 'Webcam',
  'Phone', 'Tablet', 'Charger', 'Cable', 'Adapter',
  'Paper', 'Pen', 'Pencil', 'Notebook', 'Folder',
  'Stapler', 'Projector', 'Router', 'Switch', 'Server',
  'Whiteboard', 'Marker', 'USB Drive', 'External HDD', 'SSD',
  'Smartwatch', 'Smart Speaker', 'Camera', 'Tripod', 'Microphone',
  'Lamp', 'Clock', 'Calculator', 'Speaker', 'Fan',
  'Mousepad', 'Clipboard', 'Envelope', 'Label Maker', 'Shredder'
];

const SAMPLE_UNITS = [
  'pieces', 'boxes', 'sets', 'packs', 'units',
  'cartons', 'bottles', 'tubes', 'rolls', 'bags',
  'cases', 'trays', 'bundles', 'cans', 'sheets',
  'bars', 'dozens', 'pairs', 'liters', 'meters',
  'gallons', 'ounces', 'pounds', 'kilograms', 'grams',
  'yards', 'feet', 'inches', 'centimeters', 'meters',
  'packs', 'lots', 'batches', 'sets', 'collections'
];

const SAMPLE_DESCRIPTIONS = [
  'New item in stock',
  'Needs inspection',
  'Good condition',
  'Limited quantity',
  'High priority',
  'Regular stock item',
  'Backup inventory',
  'Reserved stock',
  'Quality checked',
  'Ready for use',
  'Damaged packaging',
  'Awaiting delivery',
  'For urgent orders',
  'Returned item',
  'For training/demo',
  'Seasonal item',
  'Low stock warning',
  'Discontinued',
  'On promotion',
  'Eco-friendly',
  'Premium quality',
  'Sample for testing',
  'Bulk order',
  'To be recycled',
  'Spare part',
  'Loaned equipment',
  'Under warranty',
  'Certified refurbished',
  'Open box',
  'Used item',
  'Scratch and dent'
];

/**
 * Generate a random date within a range
 */
const generateRandomDate = (start: Date, end: Date): string => {
  const randomDate = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  return formatISO(randomDate).split('T')[0];
};

/**
 * Generate a random integer between min and max (inclusive)
 */
const getRandomInt = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * Generate a random item from an array
 */
const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

/**
 * Generate a random hex color code
 */
const generateRandomColor = (): string => {
  return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
};

/**
 * Test utility functions for application logic.
 */
export const getRandomLocationName = (index?: number) => {
  if (typeof index === 'number') {
    return SAMPLE_LOCATION_NAMES[index % SAMPLE_LOCATION_NAMES.length];
  }
  return SAMPLE_LOCATION_NAMES[Math.floor(Math.random() * SAMPLE_LOCATION_NAMES.length)];
};

export const generateTestLocations = (count: number): Location[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `loc-${index + 1}`,
    name: getRandomLocationName(index),
    description: `Test location for performance testing ${index + 1}`,
    color: generateRandomColor(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }));
};

export { SAMPLE_LOCATION_NAMES };

/**
 * Generate test items with realistic data
 */
export const generateTestItems = (count: number, locationIds: string[]): Item[] => {
  const today = new Date();
  const startDate = subDays(today, 30); // For expiry dates
  const endDate = addDays(today, 365);

  return Array.from({ length: count }, (_, index) => ({
    id: `item-${index + 1}`,
    name: `${getRandomItem(SAMPLE_ITEMS)} ${index + 1}`,
    quantity: getRandomInt(1, 100),
    unit: getRandomItem(SAMPLE_UNITS),
    locationId: getRandomItem(locationIds),
    description: getRandomItem(SAMPLE_DESCRIPTIONS),
    expiryDate: Math.random() > 0.3 ? generateRandomDate(startDate, endDate) : null,
    watchStock: Math.random() > 0.7,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }));
};

/**
 * Generate test dataset with both locations and items
 */
export const generateTestDataset = (locationCount: number, itemCount: number) => {
  const locations = generateTestLocations(locationCount);
  const locationIds = locations.map(loc => loc.id);
  const items = generateTestItems(itemCount, locationIds);

  return {
    locations,
    items
  };
}; 
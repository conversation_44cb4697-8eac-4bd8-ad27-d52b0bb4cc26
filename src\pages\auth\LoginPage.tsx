import React, { useState, useCallback, useEffect } from 'react';
import { User } from '@/types';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { BookHeart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { Link, useNavigate } from 'react-router-dom';

/**
 * LoginPage Component
 *
 * Handles user authentication by rendering a login form, validating user input,
 * submitting the login request, and displaying feedback and redirects based on
 * the login outcome.
 */
const LoginPage: React.FC = () => {
  const { login, isPendingApproval, isPasswordReset } = useAuth();
  const navigate = useNavigate();

  // --- State ---
  // Local state for form input and feedback
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInvalid, setIsInvalid] = useState(false);

  // --- Effects ---
  // Redirect user if their status requires it (pending approval or password reset)
  useEffect(() => {
    if (isPendingApproval) {
      navigate('/pending-approval');
    } else if (isPasswordReset) {
      navigate('/password-reset-pending');
    }
  }, [isPendingApproval, isPasswordReset, navigate]);

  // --- Handlers ---
  // Handle username input change and clear error state
  const handleUsernameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setUsername(e.target.value);
    setError(null);
    setIsInvalid(false);
  }, []);

  // Handle password input change and clear error state
  const handlePasswordChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    setError(null);
    setIsInvalid(false);
  }, []);

  /**
   * Handles login form submission:
   * - Validates input
   * - Calls login
   * - Handles all possible login outcomes (success, pending, reset, error)
   * - Shows relevant toasts and error messages
   */
  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    setError(null);
    setIsInvalid(false);
    setIsLoading(true); // Start loading

    if (!username.trim() || !password) {
      const msg = "Username and password are required.";
      setError(msg);
      setIsInvalid(true);
      toast.error("Login Failed", { description: msg }); // Show toast for empty fields
      setIsLoading(false); // Stop loading
      return;
    }

    try {
      const result = await login(username.trim(), password);

      if (result) {
        // Check user status
        if ('isPending' in result) {
          // User is pending - will be redirected by the useEffect
          toast.info("Account Pending", {
            description: "Your account is pending administrator approval."
          });
        } else if ('isPasswordReset' in result) {
          // User has a password reset request - will be redirected by the useEffect
          toast.info("Password Reset Pending", {
            description: "Your password reset request is being processed."
          });
        } else {
          // Login successful - Show success toast and navigate to home page
          const displayName = (result as User).fullName || (result as User).username;
          toast.success("Login Successful", { description: `Welcome back, ${displayName}!` });
          // Force navigation to home page
          navigate('/');
        }
      } else {
        // Login failed (invalid credentials or other error from context)
        const errorMsg = "Invalid username or password.";
        setError(errorMsg);
        setIsInvalid(true);
        toast.error("Login Failed", { description: errorMsg }); // Show Sonner toast on failure
      }
    } catch (err) {
      // Catch unexpected errors during the login process itself (less likely now)
      console.error("Unexpected error during login submission:", err);
      const errorMsg = "An unexpected error occurred. Please try again.";
      setError(errorMsg); // Show error in form as well
      setIsInvalid(true);
      toast.error("Login Error", { description: errorMsg }); // Show Sonner toast for unexpected errors
    } finally {
      setIsLoading(false); // Stop loading regardless of outcome
    }
  }, [username, password, login]);

  // --- Rendering ---
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background p-4 pb-20 animate-fade-in">
      {/* App Title and Description Section */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-2 mb-2">
          <BookHeart className="h-8 w-8 text-primary" />
          <h1 className="text-4xl font-bold tracking-tight">Imbentory</h1>
        </div>
        <p className="text-lg text-muted-foreground">
          A simple inventory management system.
        </p>
      </div>

      {/* Login Form Card */}
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Login</CardTitle>
          <CardDescription>Enter your credentials to access the inventory.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                type="text"
                placeholder="Enter your username"
                value={username}
                onChange={handleUsernameChange}
                required
                aria-invalid={isInvalid}
                className={cn(isInvalid && "border-destructive focus-visible:ring-destructive")}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={handlePasswordChange}
                required
                aria-invalid={isInvalid}
                className={cn(isInvalid && "border-destructive focus-visible:ring-destructive")}
              />
            </div>

            {/* Error message for invalid login attempts */}
            <p className={cn(
                "text-sm text-destructive",
                isInvalid ? "block" : "hidden"
            )}>
                {error || ""}
            </p>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Logging In...' : 'Login'}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <div className="flex justify-between w-full text-sm">
            <Link to="/forgot-password" className="text-primary hover:underline">
              Forgot password?
            </Link>
            <Link to="/signup" className="text-primary hover:underline">
              Create account
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default LoginPage;
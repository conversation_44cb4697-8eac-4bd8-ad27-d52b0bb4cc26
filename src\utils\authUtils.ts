import bcrypt from 'bcryptjs';
import { loadUsers, saveUsers } from './storageUtils';
import { User } from '@/types';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';

const SALT_ROUNDS = 10;

/**
 * Hashes a plain text password.
 */
export const hashPassword = async (password: string): Promise<string> => {
  return bcrypt.hash(password, SALT_ROUNDS);
};

/**
 * Compares a plain text password with a stored hash.
 */
export const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  return bcrypt.compare(password, hash);
};

// checkInitialSetup function removed
// createSuperAdmin function removed

/**
 * Attempts to log in a user.
 * Returns the User object on success, null otherwise.
 */
export const login = async (username: string, password: string): Promise<User | null> => {
  const users = loadUsers();
  const user = users.find(u => u.username === username);

  if (!user) {
    return null; // User not found
  }

  const isPasswordCorrect = await comparePassword(password, user.passwordHash);

  if (!isPasswordCorrect) {
    return null; // Incorrect password
  }

  // Check user status
  switch (user.status) {
    case 'pending':
      // Return the user with a pending flag
      return { ...user, isPending: true } as User & { isPending: boolean };

    case 'rejected':
      // Rejected users cannot log in
      return null;

    case 'password_reset':
      // Users with password reset requests cannot log in until admin resets their password
      // Return with a special flag
      return { ...user, isPasswordReset: true } as User & { isPasswordReset: boolean };
  }

  // Login successful
  return user;
};

// --- Admin Functions (to be implemented later) ---

/**
 * Registers a new user (by an admin).
 * Registers a new user (by an admin). Requires the calling user to have 'admin' role.
 */
export const registerUserByAdmin = async (adminUser: User, newUserInput: {
  username: string,
  password: string,
  fullName?: string,   // Add fullName parameter
  email?: string,      // Add email parameter
  avatarUrl?: string,  // Avatar URL parameter
  role?: 'admin' | 'user'
}): Promise<boolean> => {
  if (adminUser.role !== 'admin') {
    console.error("Permission denied: Only admins can register users.");
    toast.error("Permission Denied", {
      description: "Only admins can register new users."
    });
    return false;
  }

  if (!newUserInput.username.trim() || newUserInput.password.length < 6) {
     toast.error("Invalid Input", {
       description: "Username is required and password must be at least 6 characters."
     });
     return false;
  }

  const users = loadUsers();

  // Check for duplicate username
  const existingUsername = users.find(u =>
    u.username.toLowerCase() === newUserInput.username.trim().toLowerCase()
  );
  if (existingUsername) {
    toast.error("Registration Failed", {
      description: "Username already exists."
    });
    return false;
  }

  // Check for duplicate email if provided
  if (newUserInput.email) {
    const existingEmail = users.find(u =>
      u.email && u.email.toLowerCase() === newUserInput.email.trim().toLowerCase()
    );
    if (existingEmail) {
      toast.error("Registration Failed", {
        description: "Email address already in use."
      });
      return false;
    }
  }

  // Check for duplicate full name if provided
  if (newUserInput.fullName) {
    const existingFullName = users.find(u =>
      u.fullName && u.fullName.toLowerCase() === newUserInput.fullName.trim().toLowerCase()
    );
    if (existingFullName) {
      toast.error("Registration Failed", {
        description: "Full name already in use."
      });
      return false;
    }
  }

  try {
    const passwordHash = await hashPassword(newUserInput.password);
    const now = new Date().toISOString();
    const newUser: User = {
      id: uuidv4(),
      username: newUserInput.username.trim(),
      passwordHash,
      role: newUserInput.role ?? 'user',
      status: 'active', // Users created by admin are active by default
      fullName: newUserInput.fullName?.trim(),    // Include fullName if provided
      email: newUserInput.email?.trim(),          // Include email if provided
      avatarUrl: newUserInput.avatarUrl,          // Include avatarUrl if provided
      createdAt: now,
      updatedAt: now,
    };

    const updatedUsers = [...users, newUser];

    return await saveUsers(updatedUsers);
  } catch (error) {
    console.error("Failed to register user:", error);
    toast.error("Error", {
      description: "Failed to register new user."
    });
    return false;
  }
};

/**
 * Resets a user's password (by an admin).
 * Resets a user's password (by an admin). Requires the calling user to have 'admin' role.
 */
export const resetPasswordByAdmin = async (adminUser: User, targetUsername: string, newPassword: string): Promise<boolean> => {
  if (adminUser.role !== 'admin') {
    console.error("Permission denied: Only admins can reset passwords.");
    toast.error("Permission Denied", {
      description: "Only admins can reset passwords."
    });
    return false;
  }

   if (newPassword.length < 6) {
     toast.error("Invalid Input", {
       description: "New password must be at least 6 characters."
     });
     return false;
  }

  const users = loadUsers();
  const userIndex = users.findIndex(u => u.username === targetUsername);

  if (userIndex === -1) {
    toast.error("User Not Found", {
      description: `User "${targetUsername}" not found.`
    });
    return false;
  }

  try {
    const newPasswordHash = await hashPassword(newPassword);
    const updatedUser = {
      ...users[userIndex],
      passwordHash: newPasswordHash,
      updatedAt: new Date().toISOString(),
    };

    const updatedUsers = [...users];
    updatedUsers[userIndex] = updatedUser;


    return await saveUsers(updatedUsers);
  } catch (error) {
    console.error("Failed to reset password:", error);
    toast.error("Error", {
      description: "Failed to reset password."
    });
    return false;
  }
};

/**
 * Gets a list of all users (admin only).
 * Gets a list of all users (admin only). Requires the calling user to have 'admin' role.
 */
export const getUsersByAdmin = (adminUser: User): User[] => {
  if (adminUser.role !== 'admin') {
    console.error("Permission denied: Only admins can view user lists.");
    // Consider throwing an error or returning null/undefined instead of empty array
    // toast({ title: "Permission Denied", description: "Only admins can view user lists.", variant: "destructive" });
    return [];
  }
  // Return all users except potentially the admin themselves if desired? For now, return all.
  return loadUsers();
};

/**
 * Deletes a user (by an admin).
 * Deletes a user (by an admin). Requires the calling user to have 'admin' role.
 * Prevents deletion of the calling admin or the last remaining admin.
 */
export const deleteUserByAdmin = async (adminUser: User, targetUsername: string): Promise<boolean> => {
  if (adminUser.role !== 'admin') {
    console.error("Permission denied: Only admins can delete users.");
    toast.error("Permission Denied", {
      description: "Only admins can delete users."
    });
    return false;
  }

  if (adminUser.username === targetUsername) {
    toast.error("Action Denied", {
      description: "Admins cannot delete their own account."
    });
    return false;
  }

  const users = loadUsers();
  const targetUserIndex = users.findIndex(u => u.username === targetUsername);

  if (targetUserIndex === -1) {
    toast.error("User Not Found", {
      description: `User "${targetUsername}" not found.`
    });
    return false;
  }

  // Prevent deleting the last admin
  const adminCount = users.filter(u => u.role === 'admin').length;
  if (users[targetUserIndex].role === 'admin' && adminCount <= 1) {
     toast.error("Action Denied", {
       description: "Cannot delete the last remaining admin account."
     });
     return false;
  }

  try {
    const updatedUsers = users.filter(u => u.username !== targetUsername);
    return await saveUsers(updatedUsers);
  } catch (error) {
    console.error("Failed to delete user:", error);
    toast.error("Error", {
      description: "Failed to delete user."
    });
    return false;
  }
};

/**
 * Updates a user's username and optionally password (by an admin).
 * Requires the calling user to have 'admin' role.
 */
export const updateUserByAdmin = async (
  adminUser: User,
  targetUserId: string,
  updateData: {
    username: string;
    password?: string;
    fullName?: string;   // Add fullName parameter
    email?: string;      // Add email parameter
    avatarUrl?: string;  // Avatar URL parameter
    role?: 'admin' | 'user'; // Add role parameter
  }
): Promise<boolean> => {
  if (adminUser.role !== 'admin') {
    toast.error("Permission Denied", {
      description: "Only admins can update users."
    });
    return false;
  }

  if (!updateData.username.trim()) {
     toast.error("Invalid Input", {
       description: "Username cannot be empty."
     });
     return false;
  }
  // Optional: Add password validation only if password is provided
  if (updateData.password && updateData.password.length < 6) {
     toast.error("Invalid Input", {
       description: "New password must be at least 6 characters."
     });
     return false;
  }

  const users = loadUsers();
  const targetUserIndex = users.findIndex(u => u.id === targetUserId);

  if (targetUserIndex === -1) {
    toast.error("User Not Found", {
      description: `User not found.`
    });
    return false;
  }

  // Check if the new username is already taken by another user
  const newUsernameLower = updateData.username.trim().toLowerCase();
  const usernameTaken = users.some(
      (u, index) => index !== targetUserIndex && u.username.toLowerCase() === newUsernameLower
  );
  if (usernameTaken) {
      toast.error("Update Failed", {
        description: "Username is already taken by another user."
      });
      return false;
  }

  // Check if the email is already taken by another user
  if (updateData.email) {
    const newEmailLower = updateData.email.trim().toLowerCase();
    // Check if this is a new email (different from the user's current email)
    const isNewEmail = !users[targetUserIndex].email ||
      users[targetUserIndex].email.toLowerCase() !== newEmailLower;

    if (isNewEmail) {
      const emailTaken = users.some(
        (u, index) => index !== targetUserIndex && u.email && u.email.toLowerCase() === newEmailLower
      );
      if (emailTaken) {
        toast.error("Update Failed", {
          description: "Email address is already in use by another user."
        });
        return false;
      }
    }
  }

  // Check if the full name is already taken by another user
  if (updateData.fullName) {
    const newFullNameLower = updateData.fullName.trim().toLowerCase();
    // Check if this is a new full name (different from the user's current full name)
    const isNewFullName = !users[targetUserIndex].fullName ||
      users[targetUserIndex].fullName.toLowerCase() !== newFullNameLower;

    if (isNewFullName) {
      const fullNameTaken = users.some(
        (u, index) => index !== targetUserIndex && u.fullName && u.fullName.toLowerCase() === newFullNameLower
      );
      if (fullNameTaken) {
        toast.error("Update Failed", {
          description: "Full name is already in use by another user."
        });
        return false;
      }
    }
  }

  try {
    let passwordHash = users[targetUserIndex].passwordHash; // Keep old hash by default
    // Hash new password only if provided
    if (updateData.password) {
      passwordHash = await hashPassword(updateData.password);
    }

    // Check if this is a password reset user
    const isPasswordResetUser = users[targetUserIndex].status === 'password_reset';

    const updatedUser: User = {
      ...users[targetUserIndex],
      username: updateData.username.trim(),
      passwordHash, // Use new hash if password was updated, otherwise old hash
      fullName: updateData.fullName !== undefined ? updateData.fullName.trim() : users[targetUserIndex].fullName,
      email: updateData.email !== undefined ? updateData.email.trim() : users[targetUserIndex].email,
      avatarUrl: updateData.avatarUrl !== undefined ? updateData.avatarUrl : users[targetUserIndex].avatarUrl,
      // Update role if provided
      role: updateData.role !== undefined ? updateData.role : users[targetUserIndex].role,
      // If this was a password reset user and password was changed, set status back to active
      status: (isPasswordResetUser && updateData.password) ? 'active' : users[targetUserIndex].status,
      // Clear reset token if password was changed
      resetToken: (updateData.password) ? undefined : users[targetUserIndex].resetToken,
      resetTokenExpiry: (updateData.password) ? undefined : users[targetUserIndex].resetTokenExpiry,
      updatedAt: new Date().toISOString(),
    };

    const updatedUsers = [...users];
    updatedUsers[targetUserIndex] = updatedUser;

    return await saveUsers(updatedUsers);
  } catch (error) {
    console.error("Failed to update user:", error);
    toast.error("Error", {
      description: "Failed to update user."
    });
    return false;
  }
};

/**
 * Allows a logged-in user to change their own password.
 */
export const changeOwnPassword = async (
    userId: string,
    currentPassword: string,
    newPassword: string
): Promise<boolean> => {
    if (!currentPassword || !newPassword || newPassword.length < 6) {
        toast.error("Invalid Input", {
          description: "Current and new password (min 6 chars) are required."
        });
        return false;
    }

    const users = loadUsers();
    const userIndex = users.findIndex(u => u.id === userId);

    if (userIndex === -1) {
        // This shouldn't happen if the userId comes from a valid session
        toast.error("Error", {
          description: "User not found."
        });
        return false;
    }

    const user = users[userIndex];

    try {
        // Verify current password
        const isPasswordCorrect = await comparePassword(currentPassword, user.passwordHash);
        if (!isPasswordCorrect) {
            toast.error("Password Change Failed", {
              description: "Incorrect current password."
            });
            return false;
        }

        // Hash new password
        const newPasswordHash = await hashPassword(newPassword);

        // Update user
        const updatedUser: User = {
            ...user,
            passwordHash: newPasswordHash,
            updatedAt: new Date().toISOString(),
        };

        const updatedUsers = [...users];
        updatedUsers[userIndex] = updatedUser;

        return await saveUsers(updatedUsers);

    } catch (error) {
        console.error("Failed to change password:", error);
        toast.error("Error", {
          description: "Failed to change password."
        });
        return false;
    }
};

/**
 * Allows a logged-in user to update their own profile.
 */
export const updateOwnProfile = async (
    userId: string,
    updateData: {
        fullName?: string;
        email?: string;
        avatarUrl?: string;
    }
): Promise<boolean> => {
    try {
        const users = loadUsers();
        const userIndex = users.findIndex(u => u.id === userId);

        if (userIndex === -1) {
            toast.error("Error", {
              description: "User not found."
            });
            return false;
        }

        const user = users[userIndex];

        // Update user profile
        const updatedUser: User = {
            ...user,
            fullName: updateData.fullName !== undefined ? updateData.fullName.trim() : user.fullName,
            email: updateData.email !== undefined ? updateData.email.trim() : user.email,
            avatarUrl: updateData.avatarUrl !== undefined ? updateData.avatarUrl : user.avatarUrl,
            updatedAt: new Date().toISOString(),
        };

        const updatedUsers = [...users];
        updatedUsers[userIndex] = updatedUser;

        return await saveUsers(updatedUsers);
    } catch (error) {
        console.error("Failed to update profile:", error);
        toast.error("Error", {
          description: "Failed to update profile."
        });
        return false;
    }
};

/**
 * Self-registration for new users.
 * Creates a new user with 'pending' status that requires admin approval.
 */
export const registerNewUser = async (newUserInput: {
  username: string,
  password: string,
  fullName?: string,
  email?: string,
}): Promise<boolean> => {
  if (!newUserInput.username.trim() || newUserInput.password.length < 6) {
    toast.error("Invalid Input", {
      description: "Username is required and password must be at least 6 characters."
    });
    return false;
  }

  if (!newUserInput.email) {
    toast.error("Invalid Input", {
      description: "Email is required for registration."
    });
    return false;
  }

  const users = loadUsers();

  // Check for duplicate username
  const existingUsername = users.find(u =>
    u.username.toLowerCase() === newUserInput.username.trim().toLowerCase()
  );
  if (existingUsername) {
    toast.error("Registration Failed", {
      description: "Username already exists."
    });
    return false;
  }

  // Check for duplicate email
  if (newUserInput.email) {
    const existingEmail = users.find(u =>
      u.email && u.email.toLowerCase() === newUserInput.email.trim().toLowerCase()
    );
    if (existingEmail) {
      toast.error("Registration Failed", {
        description: "Email address already in use."
      });
      return false;
    }
  }

  // Check for duplicate full name
  if (newUserInput.fullName) {
    const existingFullName = users.find(u =>
      u.fullName && u.fullName.toLowerCase() === newUserInput.fullName.trim().toLowerCase()
    );
    if (existingFullName) {
      toast.error("Registration Failed", {
        description: "Full name already in use."
      });
      return false;
    }
  }

  try {
    const passwordHash = await hashPassword(newUserInput.password);
    const now = new Date().toISOString();
    const newUser: User = {
      id: uuidv4(),
      username: newUserInput.username.trim(),
      passwordHash,
      role: 'user',
      status: 'pending', // New users start with pending status
      fullName: newUserInput.fullName?.trim(),
      email: newUserInput.email?.trim(),
      createdAt: now,
      updatedAt: now,
    };

    const updatedUsers = [...users, newUser];

    return await saveUsers(updatedUsers);
  } catch (error) {
    console.error("Failed to register user:", error);
    toast.error("Error", {
      description: "Failed to register new user."
    });
    return false;
  }
};

/**
 * Generates a password reset token for a user.
 */
export const requestPasswordReset = async (email: string): Promise<boolean> => {
  if (!email.trim()) {
    toast.error("Invalid Input", {
      description: "Email is required."
    });
    return false;
  }

  const users = loadUsers();
  const userIndex = users.findIndex(u => u.email?.toLowerCase() === email.trim().toLowerCase());

  if (userIndex === -1) {
    // Don't reveal if email exists or not for security reasons
    return true; // Return true even if user not found for security
  }

  try {
    // Generate a reset token (UUID)
    const resetToken = uuidv4();
    // Set expiry to 24 hours from now
    const resetTokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();

    // Update user with reset token and set status to password_reset
    const updatedUser: User = {
      ...users[userIndex],
      resetToken,
      resetTokenExpiry,
      status: 'password_reset', // Set status to password_reset
      updatedAt: new Date().toISOString(),
    };

    const updatedUsers = [...users];
    updatedUsers[userIndex] = updatedUser;

    // Save the updated user list
    await saveUsers(updatedUsers);

    // Log for admin reference
    console.log(`Password reset requested for ${email}`);
    return true;
  } catch (error) {
    console.error("Failed to generate reset token:", error);
    toast.error("Error", {
      description: "Failed to process password reset request."
    });
    return false;
  }
};

/**
 * Resets a user's password using a valid reset token.
 */
export const resetPasswordWithToken = async (token: string, newPassword: string): Promise<boolean> => {
  if (!token || !newPassword || newPassword.length < 6) {
    toast.error("Invalid Input", {
      description: "Valid token and new password (min 6 chars) are required."
    });
    return false;
  }

  const users = loadUsers();
  const userIndex = users.findIndex(u => u.resetToken === token);

  if (userIndex === -1) {
    toast.error("Invalid Token", {
      description: "The reset token is invalid or has expired."
    });
    return false;
  }

  const user = users[userIndex];

  // Check if token has expired
  if (!user.resetTokenExpiry || new Date(user.resetTokenExpiry) < new Date()) {
    toast.error("Token Expired", {
      description: "The reset token has expired. Please request a new one."
    });
    return false;
  }

  try {
    // Hash new password
    const newPasswordHash = await hashPassword(newPassword);

    // Update user
    const updatedUser: User = {
      ...user,
      passwordHash: newPasswordHash,
      resetToken: undefined, // Clear the reset token
      resetTokenExpiry: undefined, // Clear the expiry
      updatedAt: new Date().toISOString(),
    };

    const updatedUsers = [...users];
    updatedUsers[userIndex] = updatedUser;

    return await saveUsers(updatedUsers);
  } catch (error) {
    console.error("Failed to reset password:", error);
    toast.error("Error", {
      description: "Failed to reset password."
    });
    return false;
  }
};

/**
 * Approves a pending user (admin only).
 */
export const approveUser = async (adminUser: User, userId: string): Promise<boolean> => {
  if (adminUser.role !== 'admin') {
    toast.error("Permission Denied", {
      description: "Only admins can approve users."
    });
    return false;
  }

  const users = loadUsers();
  const userIndex = users.findIndex(u => u.id === userId);

  if (userIndex === -1) {
    toast.error("User Not Found", {
      description: "The specified user could not be found."
    });
    return false;
  }

  const user = users[userIndex];

  if (user.status !== 'pending') {
    toast.error("Invalid Action", {
      description: "This user is not pending approval."
    });
    return false;
  }

  try {
    // Update user status to active
    const updatedUser: User = {
      ...user,
      status: 'active',
      updatedAt: new Date().toISOString(),
    };

    const updatedUsers = [...users];
    updatedUsers[userIndex] = updatedUser;

    return await saveUsers(updatedUsers);
  } catch (error) {
    console.error("Failed to approve user:", error);
    toast.error("Error", {
      description: "Failed to approve user."
    });
    return false;
  }
};

/**
 * Rejects a pending user (admin only).
 */
export const rejectUser = async (adminUser: User, userId: string): Promise<boolean> => {
  if (adminUser.role !== 'admin') {
    toast.error("Permission Denied", {
      description: "Only admins can reject users."
    });
    return false;
  }

  const users = loadUsers();
  const userIndex = users.findIndex(u => u.id === userId);

  if (userIndex === -1) {
    toast.error("User Not Found", {
      description: "The specified user could not be found."
    });
    return false;
  }

  const user = users[userIndex];

  if (user.status !== 'pending') {
    toast.error("Invalid Action", {
      description: "This user is not pending approval."
    });
    return false;
  }

  try {
    // Update user status to rejected
    const updatedUser: User = {
      ...user,
      status: 'rejected',
      updatedAt: new Date().toISOString(),
    };

    const updatedUsers = [...users];
    updatedUsers[userIndex] = updatedUser;

    return await saveUsers(updatedUsers);
  } catch (error) {
    console.error("Failed to reject user:", error);
    toast.error("Error", {
      description: "Failed to reject user."
    });
    return false;
  }
};
import { useState, useEffect } from 'react';
import { Settings as SettingsIcon, ShieldAlert, Palette, UserCircle, DatabaseIcon } from 'lucide-react';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { useAuth } from '@/context/AuthContext';
import { cn } from '@/lib/utils';
import { useLocation, useNavigate } from 'react-router-dom';

// Import the new components
import GeneralSettings from '@/components/settings/GeneralSettings';
import PreferencesSettings from '@/components/settings/PreferencesSettings';
import ImportExportSettings from '@/components/settings/ImportExportSettings';
import UserManagementTabContent from '@/components/settings/UserManagementTabContent';
import AdminSettings from '@/components/settings/AdminSettings';
import UserSettings from '@/components/settings/UserSettings';

const Settings = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('profile');

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tabParam = params.get('tab');
    if (tabParam && ['profile', 'preferences', 'data', 'admin'].includes(tabParam)) {
      if (tabParam === 'admin' && !isAdmin) {
        setActiveTab('profile');
      } else {
        setActiveTab(tabParam);
      }
    }
  }, [location.search, isAdmin]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const params = new URLSearchParams(location.search);
    params.set('tab', value);
    navigate({ search: params.toString() }, { replace: true });
  };

  return (
    <div className="space-y-6 animate-slide-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <SettingsIcon className="h-7 w-7 text-primary" />
          Settings
        </h1>
        <p className="text-muted-foreground">
          Manage your application preferences and data.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full space-y-4">
        <div className="border-b border-border/80">
          <TabsList className="w-full flex justify-start h-12 bg-transparent p-0">
            <TabsTrigger
              value="profile"
              className="flex items-center gap-1.5 px-4 h-12 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
            >
              <UserCircle className="h-4 w-4" />
              <span className="hidden sm:inline">Profile</span>
              <span className="inline sm:hidden">Profile</span>
            </TabsTrigger>
            <TabsTrigger
              value="preferences"
              className="flex items-center gap-1.5 px-4 h-12 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
            >
              <Palette className="h-4 w-4" />
              <span className="hidden sm:inline">Preferences</span>
              <span className="inline sm:hidden">Prefs</span>
            </TabsTrigger>
            <TabsTrigger
              value="data"
              className="flex items-center gap-1.5 px-4 h-12 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
            >
              <DatabaseIcon className="h-4 w-4" />
              <span className="hidden sm:inline">Data</span>
              <span className="inline sm:hidden">Data</span>
            </TabsTrigger>
            {isAdmin && (
              <TabsTrigger
                value="admin"
                className="flex items-center gap-1.5 px-4 h-12 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
              >
                <ShieldAlert className="h-4 w-4" />
                <span className="hidden sm:inline">Admin</span>
                <span className="inline sm:hidden">Admin</span>
              </TabsTrigger>
            )}
          </TabsList>
        </div>

        <div className="w-full">
          <TabsContent value="profile" className="mt-0">
            <UserSettings />
          </TabsContent>

          <TabsContent value="preferences" className="mt-0">
            <div className="space-y-6">
              <GeneralSettings />
              <PreferencesSettings />
            </div>
          </TabsContent>

          <TabsContent value="data" className="mt-0">
            <div className="space-y-6">
              <ImportExportSettings
                selectedFile={selectedFile}
                setSelectedFile={setSelectedFile}
                handleImport={() => {}}
              />
              {isAdmin && (
                <AdminSettings />
              )}
            </div>
          </TabsContent>

          {isAdmin && (
            <TabsContent value="admin" className="mt-0">
              <UserManagementTabContent />
            </TabsContent>
          )}
        </div>
      </Tabs>
    </div>
  );
};

export default Settings;
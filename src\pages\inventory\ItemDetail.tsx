import { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Edit, Trash2, AlertCircle, Package, Calendar, MapPin, ClipboardList, Clock, User as UserIcon, History, ArrowLeft, Tag, Eye, EyeOff, Bookmark } from 'lucide-react';
import { useInventory } from '@/context/InventoryContext';
import { loadUsers } from '@/utils/storageUtils';
import { User } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  <PERSON>ertDialog<PERSON>it<PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { format } from "date-fns";
import { formatWithTimezone } from '@/utils/dateUtils';
import { getExpiryStatusStyle } from "@/utils/itemUtils";
import BackButton from '@/components/ui/BackButton';

const getLowStockStatus = (quantity: number, watchStock: boolean): { style: string; text: string } => {
  if (!watchStock) return { style: '', text: '' };

  const threshold = parseInt(localStorage.getItem('low-stock-threshold') || '3');

  if (quantity <= 0) {
    return { style: 'text-destructive', text: 'Out of Stock' };
  }

  if (quantity <= threshold) {
    return { style: 'text-warning', text: 'Low Stock' };
  }

  return { style: '', text: '' };
};

const ItemDetail = () => {
  const { itemId } = useParams<{ itemId: string }>();
  const { items, getLocationById, deleteItem } = useInventory();
  const navigate = useNavigate();
  const [users, setUsers] = useState<User[]>([]);

  // Load users once on mount to map IDs to names
  useEffect(() => {
    setUsers(loadUsers());
  }, []);

  // Create a map for quick username lookup
  const userMap = useMemo(() => {
    const map = new Map<string, string>();
    users.forEach(user => map.set(user.id, user.username));
    return map;
  }, [users]);

  if (!itemId) {
    navigate(-1);
    return null;
  }

  const item = items.find(i => i.id === itemId);

  if (!item) {
    navigate(-1);
    return null;
  }

  const location = getLocationById(item.locationId);

  const handleDelete = () => {
    deleteItem(itemId);
    navigate('/items');
  };

  return (
    <div className="animate-slide-in max-w-6xl">
      {/* Header with item name */}
      <div className="flex gap-2">
        <BackButton />
      </div>
      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Item details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Item header card */}
          <Card className="overflow-hidden shadow-sm py-0">
            <div className="bg-gradient-to-r from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10 p-3">
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-4">
                  <div className="bg-background dark:bg-muted p-3 rounded-xl shadow-sm">
                    <Package className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold tracking-tight">{item.name}</h1>
                    <div className="flex items-center gap-2 mt-1 text-muted-foreground">
                      <Badge variant="outline" className="font-normal">
                        <Tag className="h-3 w-3 mr-1" />
                        ID: {itemId.substring(0, 8)}
                      </Badge>
                      <Badge variant={item.watchStock ? "secondary" : "outline"} className="font-normal">
                        {item.watchStock ? (
                          <>
                            <Eye className="h-3 w-3 mr-1" />
                            Stock monitored
                          </>
                        ) : (
                          <>
                            <EyeOff className="h-3 w-3 mr-1" />
                            Stock not monitored
                          </>
                        )}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => navigate(`/edit-item/${itemId}`)} size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will permanently delete the item "{item.name}" from your inventory.
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDelete}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </div>

            <div className='px-4'>
              {/* Tabs for details and history */}
              <Tabs defaultValue="details" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="details" className="flex items-center gap-1">
                    <ClipboardList className="h-4 w-4" />
                    Details
                  </TabsTrigger>
                  <TabsTrigger value="history" className="flex items-center gap-1">
                    <History className="h-4 w-4" />
                    History
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="details" className="mt-4">
                  <Card className="gap-1">
                    <CardHeader>
                      <CardTitle className="text-lg">Item Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Description */}
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1">
                          <ClipboardList className="h-4 w-4" /> Description
                        </h3>
                        <div className="p-4 rounded-md bg-muted/50 dark:bg-muted/30 border border-border/50">
                          {item.description ? (
                            <p>{item.description}</p>
                          ) : (
                            <p className="text-muted-foreground italic">No description provided</p>
                          )}
                        </div>
                      </div>

                      {/* Additional details can be added here */}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="history" className="mt-4">
                  <Card className="gap-1">
                    <CardHeader>
                      <CardTitle className="text-lg">Item History</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 rounded-md bg-muted/50 dark:bg-muted/30 border border-border/50">
                          <h3 className="text-sm font-medium text-muted-foreground mb-2 flex items-center gap-1">
                            <Clock className="h-4 w-4" /> Created
                          </h3>
                          <div className="flex items-center gap-2">
                            <div className="bg-primary/10 p-2 rounded-full">
                              <UserIcon className="h-5 w-5 text-primary" />
                            </div>
                            <div>
                              <div className="font-medium">
                                {formatWithTimezone(item.createdAt, 'PPp', true)}
                              </div>
                              {item.createdByUserId && (
                                <div className="text-sm text-muted-foreground">
                                  by {userMap.get(item.createdByUserId) || 'Unknown User'}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="p-4 rounded-md bg-muted/50 dark:bg-muted/30 border border-border/50">
                          <h3 className="text-sm font-medium text-muted-foreground mb-2 flex items-center gap-1">
                            <History className="h-4 w-4" /> Last Updated
                          </h3>
                          <div className="flex items-center gap-2">
                            <div className="bg-primary/10 p-2 rounded-full">
                              <UserIcon className="h-5 w-5 text-primary" />
                            </div>
                            <div>
                              <div className="font-medium">
                                {formatWithTimezone(item.updatedAt, 'PPp', true)}
                              </div>
                              {item.updatedByUserId && (
                                <div className="text-sm text-muted-foreground">
                                  by {userMap.get(item.updatedByUserId) || 'Unknown User'}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
            {/* Key metrics */}
            <div className="grid grid-cols-3 border-t border-border/50">
              <div className="p-2 text-center border-r border-border/50">
                <div className="text-sm text-muted-foreground mb-1">Quantity</div>
                <div className="text-lg font-bold flex items-center justify-center gap-1">
                  {item.quantity}
                  <span className="text-sm font-normal text-muted-foreground">{item.unit}</span>
                </div>
                {item.watchStock && getLowStockStatus(item.quantity, item.watchStock).text && (
                  <Badge variant={item.quantity <= 0 ? "destructive" : "secondary"} className="mt-1">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    {getLowStockStatus(item.quantity, item.watchStock).text}
                  </Badge>
                )}
              </div>

              <div className="p-2 text-center border-r border-border/50">
                <div className="text-sm text-muted-foreground mb-1">Location</div>
                <div className="text-md font-medium">
                  <Button
                    variant="link"
                    className="text-md p-0 h-auto font-medium"
                    onClick={() => navigate(`/locations/${item.locationId}`)}
                  >
                    <MapPin className="h-4 w-4 mr-1 text-primary" />
                    {location?.name || "Unknown location"}
                  </Button>
                </div>
              </div>

              <div className="p-2 text-center">
                <div className="text-sm text-muted-foreground mb-1">Expiry Date</div>
                {item.expiryDate ? (
                  <div className={`text-md font-medium ${getExpiryStatusStyle(item.expiryDate)}`}>
                    <Calendar className="h-4 w-4 mr-1 inline-block" />
                    {format(new Date(item.expiryDate), 'MMM d, yyyy')}
                  </div>
                ) : (
                  <div className="text-muted-foreground text-sm">No expiry date</div>
                )}
              </div>
            </div>
          </Card>

        </div>

        {/* Right column - Related information */}
        <div className="space-y-6">
          {/* Location card */}
          <Card className="gap-1">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <MapPin className="h-4 w-4 text-primary" />
                Location Details
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-3">
              {location ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-md flex items-center justify-center"
                      style={{ backgroundColor: location.color || '#888888' }}>
                      <MapPin className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <div className="font-medium">{location.name}</div>
                      {location.description && (
                        <div className="text-sm text-muted-foreground">{location.description}</div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-muted-foreground">Location information not available</div>
              )}
            </CardContent>
            <CardFooter>
              {location && (
                <Button variant="outline" className="w-full" onClick={() => navigate(`/locations/${item.locationId}`)}>
                  <MapPin className="mr-2 h-4 w-4" />
                  View Location
                </Button>
              )}
            </CardFooter>
          </Card>

          {/* Quick actions card */}
          <Card className="gap-1 pb-2">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="pb-3 space-y-2">
              <Button variant="outline" className="w-full justify-start" onClick={() => navigate(`/edit-item/${itemId}`)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Item
              </Button>

              <Button variant="outline" className="w-full justify-start" onClick={() => navigate('/add-item', { state: { duplicateFrom: item } })}>
                <Bookmark className="mr-2 h-4 w-4" />
                Duplicate Item
              </Button>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-destructive hover:text-destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Item
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will permanently delete the item "{item.name}" from your inventory.
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDelete}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ItemDetail;

import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import {
  Package, Plus, Loader2, Edit, Trash2,
  ChevronDown, ChevronUp, ChevronsUpDown, Search
} from 'lucide-react';

// Import UI components used for rendering the main table and its controls.
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import DataTablePagination from '@/components/items-table/DataTablePagination';

// Import utility functions for class names and item status styling.
import { cn } from '@/lib/utils';
import { getExpiryStatusStyle, getLowStockStatusStyle } from '@/utils/itemUtils';
import LocationColorSwatch from './LocationColorSwatch'; // Visual indicator for item location color

// Import TypeScript types for items and locations.
import { Item, Location } from "@/types";


interface DataTableProps {
  items: Item[];
  locations: Location[];
  getLocationById: (id: string) => Location | undefined;
  isLoading: boolean;
  paginatedItems: Item[];
  sortField: string;
  sortDirection: 'asc' | 'desc';
  itemsPerPage: number;
  currentPage: number;
  totalPages: number;
  startIndex: number;
  sortedItems: Item[];
  selectedItems: Set<string>;
  showLocationColumn?: boolean;
  emptyMessage?: {
    title: string;
    description: string;
    buttonText?: string;
    buttonAction?: () => void;
    icon?: React.ReactNode;
  };
  onToggleSort: (field: string) => void;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (value: number) => void;
  onSelectItem: (id: string) => void;
  onSelectAll: () => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onItemClick?: (id: string) => void;
}

interface TableColumn {
  field: string;
  label: string;
  sortable: boolean;
  className?: string;
}

// Table subcomponents for rendering header, rows, and skeleton states.

const HeaderCell = ({
  field, label, sortField, sortDirection, onSort, className = ""
}: {
  field: string;
  label: string;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (field: string) => void;
  className?: string;
}) => {
  const isSorted = sortField === field;

  if (!onSort) {
    return <TableHead className={className}>{label}</TableHead>;
  }

  return (
    <TableHead className={className}>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-8 px-0 py-0 font-medium hover:bg-transparent text-left flex items-center",
          isSorted && "text-foreground"
        )}
        onClick={() => onSort(field)}
      >
        {label}
        <span className="ml-1.5 flex items-center">
          {isSorted ? (
            sortDirection === 'asc' ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )
          ) : (
            <ChevronsUpDown className="h-3.5 w-3.5 text-muted-foreground/70" />
          )}
        </span>
      </Button>
    </TableHead>
  );
};

const ItemRow = ({
  item, location, isSelected, onSelect, onEdit, onDelete, navigate, showLocation = true
}: {
  item: Item;
  location: Location | undefined;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onEdit: () => void;
  onDelete: (id: string) => void;
  navigate: (path: string) => void;
  showLocation?: boolean;
}) => (
  <TableRow className={cn(isSelected && "bg-muted/50", "border-border/50")}>
    <TableCell className="w-[40px]">
      <div className="flex items-center justify-center">
        <Checkbox checked={isSelected} onCheckedChange={() => onSelect(item.id)} />
      </div>
    </TableCell>
    <TableCell className="py-2 font-medium">
      <Button
        variant="link"
        className="p-0 h-auto font-medium text-foreground hover:text-primary"
        onClick={() => navigate(`/items/${item.id}`)}
      >
        {item.name}
      </Button>
    </TableCell>
    <TableCell className="py-2 whitespace-nowrap">
      <span className={getLowStockStatusStyle(item.quantity, item.watchStock)}>
        {item.quantity} {item.unit}
      </span>
    </TableCell>
    {showLocation && (
      <TableCell className="hidden sm:table-cell py-2">
        <Button
          variant="link"
          className="p-0 h-auto text-sm hover:text-primary flex items-center gap-1.5"
          onClick={() => navigate(`/locations/${item.locationId}`)}
        >
          <LocationColorSwatch color={location?.color} />
          <span>{location?.name || 'N/A'}</span>
        </Button>
      </TableCell>
    )}
    <TableCell className="hidden md:table-cell py-2">
      <span className={getExpiryStatusStyle(item.expiryDate)}>
        {item.expiryDate
          ? format(new Date(item.expiryDate), 'MMM d, yyyy')
          : "—"}
      </span>
    </TableCell>
    <TableCell className="hidden lg:table-cell py-2">
      <span className="line-clamp-1 text-sm text-muted-foreground">{item.description || "—"}</span>
    </TableCell>
    <TableCell>
      <div className="flex gap-1">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={onEdit}
        >
          <Edit className="h-4 w-4" />
          <span className="sr-only">Edit</span>
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 hover:text-destructive hover:bg-destructive/10"
          onClick={() => onDelete(item.id)}
        >
          <Trash2 className="h-4 w-4" />
          <span className="sr-only">Delete</span>
        </Button>
      </div>
    </TableCell>
  </TableRow>
);

const RowSkeleton = ({ columns = 7 }: { columns?: number }) => {
  const cells = [];

    // Render a skeleton cell for the checkbox column.
  cells.push(
    <TableCell key="checkbox" className="w-[40px]">
      <div className="flex items-center justify-center">
        <Skeleton className="h-4 w-4 rounded-sm" />
      </div>
    </TableCell>
  );

    // Render skeleton cells for data columns.
  for (let i = 1; i < columns - 1; i++) {
    const width = i === 1 ? "w-[150px]" : i === 2 ? "w-[60px]" : "w-[100px]";
    cells.push(
      <TableCell key={`cell-${i}`} className="px-4 py-2">
        <Skeleton className={`h-4 ${width}`} />
      </TableCell>
    );
  }

    // Render a skeleton cell for the actions column.
  cells.push(
    <TableCell key="actions" className="px-4 py-2">
      <div className="flex justify-end gap-2">
        <Skeleton className="h-8 w-8 rounded-md" />
        <Skeleton className="h-8 w-8 rounded-md" />
      </div>
    </TableCell>
  );

  return <TableRow className="border-border">{cells}</TableRow>;
};

const ItemCard = ({
  item, location, isSelected, onSelect, onEdit, onDelete, navigate, showLocation = true
}: {
  item: Item;
  location: Location | undefined;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onEdit: () => void;
  onDelete: (id: string) => void;
  navigate: (path: string) => void;
  showLocation?: boolean;
}) => {
  const expiryStatusClass = getExpiryStatusStyle(item.expiryDate);

  return (
    <div className="p-3">
      <div className="flex items-start">
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onSelect(item.id)}
          className="mt-1 mr-3"
        />

        <div className="flex-1">
          <div className="flex items-center gap-2">
            <Button
              variant="link"
              className="p-0 h-auto text-lg font-medium hover:text-primary"
              onClick={() => navigate(`/items/${item.id}`)}
            >
              {item.name}
            </Button>

            {item.expiryDate && (
              <Badge variant="outline" className={expiryStatusClass + " text-xs"}>
                {format(new Date(item.expiryDate), 'MMM d, yyyy')}
              </Badge>
            )}
          </div>

          <div className="text-md mt-1">
            {item.quantity} {item.unit}
          </div>

          {showLocation && (
            <Button
              variant="link"
              className="p-0 h-auto text-sm hover:text-primary flex items-center gap-1.5"
              onClick={() => navigate(`/locations/${item.locationId}`)}
            >
              <LocationColorSwatch color={location?.color} />
              <span>{location?.name || 'N/A'}</span>
            </Button>
          )}

          <div className="text-sm text-muted-foreground mt-0.5">
            Regular {item.description || "stock item"}
          </div>
        </div>

        <div className="flex justify-end gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={onEdit}
          >
            <Edit className="h-4 w-4" />
            <span className="sr-only">Edit</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:text-destructive hover:bg-destructive/10"
            onClick={() => onDelete(item.id)}
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">Delete</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

const CardSkeleton = () => (
  <div className="p-3">
    <div className="flex items-start gap-3">
      <Skeleton className="h-4 w-4 rounded-sm mt-1" />
      <div className="flex-1">
        <Skeleton className="h-5 w-[140px] mb-2" />
        <Skeleton className="h-4 w-[80px] mb-1" />
        <Skeleton className="h-4 w-[100px] mb-1" />
        <Skeleton className="h-4 w-[150px]" />
      </div>
      <div className="flex gap-1">
        <Skeleton className="h-8 w-8 rounded-md" />
        <Skeleton className="h-8 w-8 rounded-md" />
      </div>
    </div>
    <div className="flex justify-end mt-1">
      <Skeleton className="h-4 w-[100px]" />
    </div>
  </div>
);

import EmptyState from '@/components/ui/EmptyState';

// Main DataTable component implementation.

const DataTable = ({
  getLocationById,
  isLoading,
  paginatedItems,
  sortField,
  sortDirection,
  itemsPerPage,
  currentPage,
  totalPages,
  startIndex,
  sortedItems,
  selectedItems,
  showLocationColumn = true,
  emptyMessage = {
    title: "No items found",
    description: "There are no items to display."
  },
  onToggleSort,
  onPageChange,
  // onItemsPerPageChange is intentionally unused here; pagination is handled elsewhere.
  onSelectItem,
  onSelectAll,
  onEdit,
  onDelete,
}: DataTableProps) => {
  const navigate = useNavigate();

  // Define the columns to display in the table.
  const columns = useMemo<TableColumn[]>(() => {
    const baseColumns = [
      {
        field: "name",
        label: "Name",
        sortable: true,
        className: "w-[130px]"
      },
      {
        field: "quantity",
        label: "Quantity",
        sortable: true,
        className: "w-[100px]"
      }
    ];

    if (showLocationColumn) {
      baseColumns.push({
        field: "location",
        label: "Location",
        sortable: false,
        className: "hidden sm:table-cell w-[150px]"
      });
    }

    return [
      ...baseColumns,
      {
        field: "expiryDate",
        label: "Expiry Date",
        sortable: true,
        className: "hidden md:table-cell w-[140px] text-left"
      },
      {
        field: "description",
        label: "Description",
        sortable: false,
        className: "hidden lg:table-cell"
      },
      {
        field: "actions",
        label: "",
        sortable: false,
        className: "w-[100px]"
      }
    ];
  }, [showLocationColumn]);

  // Show skeleton loaders while data is being fetched.
  if (isLoading) {
    return (
      <div className="w-full space-y-4">
        {/* Desktop loading skeleton */}
        <div className="rounded-md border border-border hidden md:block">
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="w-[40px]">
                  <div className="flex items-center justify-center h-4 w-4">
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  </div>
                </TableHead>
                {columns.map((column) => (
                  <TableHead key={column.field} className={column.className}>
                    {column.label}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody className="border-border">
              {Array.from({ length: 3 }).map((_, index) => (
                <RowSkeleton key={index} columns={columns.length + 1} />
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Mobile loading skeleton */}
        <div className="md:hidden space-y-3">
          {Array.from({ length: 2 }).map((_, index) => (
            <Card key={index}><CardSkeleton /></Card>
          ))}
        </div>
      </div>
    );
  }

  // Show an empty state when there are no items to display.
  if (sortedItems.length === 0) {
    return (
      <EmptyState
        title={emptyMessage?.title ?? "No items found"}
        description={emptyMessage?.description ?? "There are no items to display."}
        buttonText={emptyMessage?.buttonText}
        buttonAction={emptyMessage?.buttonAction}
        icon={emptyMessage?.icon ?? (
          emptyMessage?.title?.toLowerCase().includes('search')
            ? <Search className="h-6 w-6 text-muted-foreground" />
            : <Package className="h-6 w-6 text-muted-foreground" />
        )}
      />
    );
  }

  return (
    <div className="w-full space-y-4">
      {/* Desktop Table */}
      <div className="hidden md:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <div className="flex items-center justify-center">
                  <Checkbox
                    checked={selectedItems.size === paginatedItems.length && paginatedItems.length > 0}
                    onCheckedChange={onSelectAll}
                    aria-label="Select all"
                  />
                </div>
              </TableHead>
              {columns.map((column) => (
                <HeaderCell
                  key={column.field}
                  field={column.field}
                  label={column.label}
                  sortField={sortField}
                  sortDirection={sortDirection}
                  onSort={column.sortable ? onToggleSort : undefined}
                  className={column.className}
                />
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + 1} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            ) : (
              paginatedItems.map(item => (
                <ItemRow
                  key={item.id}
                  item={item}
                  location={getLocationById(item.locationId)}
                  isSelected={selectedItems.has(item.id)}
                  onSelect={() => onSelectItem(item.id)}
                  onEdit={() => onEdit(item.id)}
                  onDelete={() => onDelete(item.id)}
                  navigate={navigate}
                  showLocation={showLocationColumn}
                />
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden space-y-3">
        {paginatedItems.map(item => (
          <Card key={item.id} className="bg-card rounded-xl py-0 pb-1">
            <ItemCard
              item={item}
              location={getLocationById(item.locationId)}
              isSelected={selectedItems.has(item.id)}
              onSelect={() => onSelectItem(item.id)}
              onEdit={() => onEdit(item.id)}
              onDelete={() => onDelete(item.id)}
              navigate={navigate}
              showLocation={showLocationColumn}
            />
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <DataTablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={sortedItems.length}
        startIndex={startIndex}
        itemsPerPage={itemsPerPage}
        onPageChange={onPageChange}
      />
    </div>
  );
};

export default DataTable;
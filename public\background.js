// Background script for handling IndexedDB migrations
const DB_VERSION = 70; // Set to the highest version number from the errors (70)

// List of migrator versions to handle
const migratorVersions = [
  { name: 'Rf', version: 53 },
  { name: 'Uf', version: 54 },
  { name: '$f', version: 55 },
  { name: 'Yf', version: 56 },
  { name: 'ey', version: 57 },
  { name: 'ny', version: 58 },
  { name: 'oy', version: 59 },
  { name: 'gy', version: 60 },
  { name: 'Ay', version: 61 },
  { name: 'By', version: 62 },
  { name: 'Ty', version: 63 },
  { name: 'Oy', version: 64 },
  { name: '<PERSON><PERSON>', version: 65 },
  { name: 'ev', version: 66 },
  { name: 'nv', version: 67 },
  { name: 'sv', version: 68 },
  { name: 'uv', version: 69 },
  { name: 'mv', version: 70 }
];

// Open the database with the correct version
function openDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('imbentory-db', DB_VERSION);

    // Handle database upgrade
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      const oldVersion = event.oldVersion;

      // Create object stores if they don't exist
      if (!db.objectStoreNames.contains('items')) {
        db.createObjectStore('items', { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains('locations')) {
        db.createObjectStore('locations', { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains('settings')) {
        db.createObjectStore('settings', { keyPath: 'id' });
      }

      // Create a migrations store to track migrator versions
      if (!db.objectStoreNames.contains('migrations')) {
        const migrationsStore = db.createObjectStore('migrations', { keyPath: 'name' });

        // Add each migrator to the store
        migratorVersions.forEach(migrator => {
          migrationsStore.add({
            name: migrator.name,
            version: migrator.version,
            migrated: true,
            timestamp: new Date().toISOString()
          });
        });
      }

      console.log(`Database upgraded from version ${oldVersion} to ${DB_VERSION}`);
    };

    request.onsuccess = (event) => {
      const db = event.target.result;
      resolve(db);
    };

    request.onerror = (event) => {
      console.error('Error opening database:', event.target.error);
      reject(event.target.error);
    };
  });
}

// Initialize the database when the service worker starts
openDatabase().then(() => {
  console.log('Database initialized successfully');
}).catch(error => {
  console.error('Failed to initialize database:', error);
});

// Listen for messages from the main application
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'INIT_DB') {
    openDatabase().then(() => {
      event.ports[0].postMessage({ success: true });
    }).catch(error => {
      event.ports[0].postMessage({ success: false, error: error.toString() });
    });
  }
});

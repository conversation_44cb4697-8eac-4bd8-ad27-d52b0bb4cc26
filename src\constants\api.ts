/**
 * api.ts
 *
 * Centralized API-related constants for endpoints, request settings, and response types.
 * Used for both future backend integration and local storage simulation.
 * Provides a single source of truth for API configuration and messaging.
 */

// API endpoints for interacting with backend services
export const API_ENDPOINTS = {
  ITEMS: '/api/items',
  LOCATIONS: '/api/locations',
  SETTINGS: '/api/settings',
  AUTH: '/api/auth',
  IMPORT: '/api/import',
  EXPORT: '/api/export'
};

// Request timeouts
export const REQUEST_TIMEOUT = 30000; // 30 seconds

// Cache durations (in ms)
export const CACHE_DURATION = {
  SHORT: 60 * 1000,        // 1 minute
  MEDIUM: 5 * 60 * 1000,   // 5 minutes
  LONG: 60 * 60 * 1000,    // 1 hour
  EXTENDED: 24 * 60 * 60 * 1000 // 24 hours
};

// Status codes
export const STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
};

// Response messages
export const RESPONSE_MESSAGES = {
  SUCCESS: 'Operation completed successfully',
  ERROR: 'An error occurred while processing your request',
  NOT_FOUND: 'The requested resource was not found',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  VALIDATION_ERROR: 'Please check the provided data and try again'
};

// Local storage version for data migrations
export const STORAGE_VERSION = '1.0';

// Export/Import formats
export const EXPORT_FORMATS = ['json', 'csv'] as const;

// Default request headers
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}; 
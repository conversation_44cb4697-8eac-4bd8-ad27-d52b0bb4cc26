import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { BookHeart, Clock, LogOut } from 'lucide-react';
import { toast } from 'sonner';

const PendingApprovalPage: React.FC = () => {
  const { logout } = useAuth();
  
  const handleLogout = () => {
    logout();
    toast.info("Logged Out", {
      description: "You have been logged out successfully."
    });
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-md space-y-8">
        <div className="flex flex-col items-center text-center">
          <BookHeart className="h-12 w-12 text-primary" />
          <h1 className="mt-2 text-2xl font-bold">Imbentorys</h1>
          <p className="text-sm text-muted-foreground">Account Pending Approval</p>
        </div>

        <Card className="w-full">
          <CardHeader>
            <div className="flex items-center justify-center mb-4">
              <div className="h-16 w-16 rounded-full bg-amber-100 flex items-center justify-center">
                <Clock className="h-8 w-8 text-amber-600" />
              </div>
            </div>
            <CardTitle className="text-center">Account Pending Approval</CardTitle>
            <CardDescription className="text-center">
              Your account is waiting for administrator approval
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-muted p-4 rounded-md text-sm">
              <p className="mb-2">Thank you for registering! Your account has been created but requires administrator approval before you can log in.</p>
              <p>Please check back later or contact your administrator for assistance.</p>
            </div>
            
            <div className="flex justify-center">
              <Button 
                variant="outline" 
                className="flex items-center gap-2"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4" />
                Return to Login
              </Button>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <p className="text-xs text-muted-foreground">
              If you believe this is an error, please contact your system administrator.
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default PendingApprovalPage;

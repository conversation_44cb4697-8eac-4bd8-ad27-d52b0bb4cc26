import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Plus, Save, Trash2 } from 'lucide-react';
import { useInventory } from '@/context/InventoryContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { handleUnitChange } from "@/utils/itemUtils";
import { validateItemFields, validateItemName } from "@/utils/formUtils";
import { Item } from "@/types";
import BackButton from '@/components/ui/BackButton';

interface ItemRow {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  description: string;
  expiryDate: string;
  watchStock: boolean;
  isValid: boolean;
  isCustomUnit: boolean;
  error?: string;
}

const createEmptyRow = (): ItemRow => ({
  id: crypto.randomUUID(),
  name: '',
  quantity: '',
  unit: '',
  description: '',
  expiryDate: '',
  watchStock: false,
  isValid: false,
  isCustomUnit: false,
});

const BatchAddItems = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { addItem, getLocationById, items } = useInventory();
  const locationId = location.state?.locationId;
  const locationDetails = locationId ? getLocationById(locationId) : null;

  const [rows, setRows] = useState<ItemRow[]>([createEmptyRow()]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Extract unique units from all items for unit dropdown
  const existingUnits = Array.from(new Set(items.map(item => item.unit))).filter(Boolean);

  if (!locationId || !locationDetails) {
    navigate('/locations');
    return null;
  }

  const validateRow = (row: ItemRow, items: Item[], locationId: string): { isValid: boolean; error: string } => {
    // Validate the item name for uniqueness and required constraints
    const nameValidation = validateItemName(row.name, items, locationId);
    if (!nameValidation.isValid) {
      return nameValidation;
    }

    // Validate all other required item fields
    const itemFields = {
      name: row.name.trim(),
      quantity: Number(row.quantity),
      unit: row.unit.trim(),
      locationId,
      description: row.description.trim(),
      expiryDate: row.expiryDate || null,
      watchStock: row.watchStock
    };

    return validateItemFields(itemFields);
  };

  const handleRowChange = (id: string, field: keyof ItemRow, value: string | boolean) => {
    setRows(currentRows => {
      return currentRows.map(row => {
        if (row.id === id) {
          const updatedRow = { ...row, [field]: value };
          const validation = validateRow(updatedRow, items, locationId);
          return {
            ...updatedRow,
            isValid: validation.isValid,
            error: validation.error
          };
        }
        return row;
      });
    });
  };

  const handleRowUnitChange = (id: string) => handleUnitChange(
    (value) => handleRowChange(id, 'unit', value),
    (isCustom) => setRows(currentRows => 
      currentRows.map(row => 
        row.id === id 
          ? { ...row, isCustomUnit: isCustom, unit: '', isValid: validateRow({ ...row, unit: '' }, items, locationId).isValid }
          : row
      )
    )
  );

  const cancelCustomUnit = (id: string) => {
    setRows(currentRows => {
      return currentRows.map(row => {
        if (row.id === id) {
          return {
            ...row,
            unit: '',
            isCustomUnit: false,
            isValid: validateRow({ ...row, unit: '' }, items, locationId).isValid
          };
        }
        return row;
      });
    });
  };

  const addNewRow = () => {
    setRows(currentRows => [...currentRows, createEmptyRow()]);
  };

  const removeRow = (id: string) => {
    if (rows.length === 1) {
      setRows([createEmptyRow()]);
    } else {
      setRows(currentRows => currentRows.filter(row => row.id !== id));
    }
  };

  const handleSubmit = async () => {
    const validRows = rows.filter(row => row.isValid);
    if (validRows.length === 0) {
      const invalidRows = rows.filter(row => !row.isValid);
      const firstError = invalidRows[0]?.error || "Please fill in at least one row with valid data.";
      
      toast.error("Validation Error", {
        description: firstError
      });
      return;
    }

    setIsSubmitting(true);
    try {
      for (const row of validRows) {
        await addItem({
          name: row.name.trim(),
          quantity: Number(row.quantity),
          unit: row.unit.trim(),
          description: row.description.trim() || '',
          expiryDate: row.expiryDate || null,
          locationId,
          watchStock: row.watchStock,
        });
      }

      toast.success("Success", {
        description: `Added ${validRows.length} items to ${locationDetails.name}`
      });
      navigate(`/locations/${locationId}`);
    } catch (error) {
      toast.error("Error", {
        description: "Failed to add items. Please try again."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const clipboardData = e.clipboardData.getData('text');
    const rows = clipboardData.split('\\n').filter(row => row.trim());
    
    if (rows.length === 0) return;

    const newRows = rows.map(row => {
      const [name, quantity, unit, description, expiryDate] = row.split('\\t').map(cell => cell.trim());
      const newRow = {
        id: crypto.randomUUID(),
        name: name || '',
        quantity: quantity || '',
        unit: unit || '',
        description: description || '',
        expiryDate: expiryDate || '',
        watchStock: false,
        isValid: false,
        isCustomUnit: false
      };
      return { ...newRow, isValid: validateRow(newRow, items, locationId).isValid };
    });

    setRows(newRows);
  };

  

  return (
    // Layout wrapper removed
      <div className="space-y-6 animate-slide-in">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">Batch Add Items</h1>
            <p className="text-muted-foreground">
              Add multiple items to {locationDetails.name} in one go
            </p>
          </div>
          <div className="flex gap-2">
            <BackButton />
          </div>
        </div>

        <Card className="card">
          <CardHeader>
            <CardTitle>Batch Item Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {rows.map((row, index) => (
                // Add border and adjust padding for visual clarity
                <div key={row.id} className={`py-3 pt-0  ${index < rows.length - 1 ? 'border-b border-border/40' : ''}`}>
                  <div className="grid grid-cols-12 gap-2 items-center">
                    {/* Name */}
                    <div className="col-span-12 sm:col-span-6 md:col-span-3">
                       <Label htmlFor={`name-${row.id}`} className="sr-only">Name</Label>
                      <Input
                        id={`name-${row.id}`}
                        placeholder="Item name"
                        value={row.name}
                        onChange={(e) => handleRowChange(row.id, 'name', e.target.value)}
                        onPaste={index === 0 ? handlePaste : undefined}
                        className={row.error ? "border-destructive" : ""}
                      />
                      {row.error && (
                        <p className="text-xs text-destructive mt-1">{row.error}</p> // Style error message with smaller text
                      )}
                    </div>
                    {/* Quantity */}
                    <div className="col-span-4 sm:col-span-2 md:col-span-1">
                       <Label htmlFor={`quantity-${row.id}`} className="sr-only">Quantity</Label>
                      <Input
                        id={`quantity-${row.id}`}
                        placeholder="Qty"
                        type="number"
                        min="0"
                        value={row.quantity}
                        onChange={(e) => handleRowChange(row.id, 'quantity', e.target.value)}
                      />
                    </div>
                    {/* Unit */}
                    <div className="col-span-8 sm:col-span-4 md:col-span-2">
                       <Label htmlFor={`unit-${row.id}`} className="sr-only">Unit</Label>
                      {!row.isCustomUnit ? (
                        <Select value={row.unit} onValueChange={(value) => handleRowUnitChange(row.id)(value)}>
                          <SelectTrigger id={`unit-${row.id}`}>
                            <SelectValue placeholder="Unit" />
                          </SelectTrigger>
                          <SelectContent>
                            {existingUnits.map(existingUnit => (
                              <SelectItem key={existingUnit} value={existingUnit}>
                                {existingUnit}
                              </SelectItem>
                            ))}
                            <SelectItem value="custom">Custom</SelectItem>
                          </SelectContent>
                        </Select>
                      ) : (
                        <div className="flex gap-1"> {/* Reduced gap */}
                          <Input
                            placeholder="Custom unit"
                            value={row.unit}
                            onChange={(e) => handleRowChange(row.id, 'unit', e.target.value)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => cancelCustomUnit(row.id)}
                            className="h-9 w-9"
                          >
                            ×
                          </Button>
                        </div>
                      )}
                    </div>
                    {/* Expiry Date */}
                    <div className="col-span-6 sm:col-span-4 md:col-span-2">
                       <Label htmlFor={`expiry-${row.id}`} className="sr-only">Expiry Date</Label>
                      <Input
                        id={`expiry-${row.id}`}
                        type="date"
                        value={row.expiryDate}
                        onChange={(e) => handleRowChange(row.id, 'expiryDate', e.target.value)}
                      />
                    </div>
                     {/* Watch Stock */}
                     <div className="col-span-6 sm:col-span-3 md:col-span-2 flex items-center justify-start sm:justify-center pt-2 sm:pt-0">
                       <div className="flex items-center space-x-2">
                         <Checkbox
                           id={`watchStock-${row.id}`}
                           checked={row.watchStock}
                           onCheckedChange={(checked) => handleRowChange(row.id, 'watchStock', checked as boolean)}
                         />
                         <Label htmlFor={`watchStock-${row.id}`} className="whitespace-nowrap">Watch Stock</Label>
                       </div>
                     </div>
                     {/* Remove Button */}
                     <div className="col-span-12 sm:col-span-1 flex justify-end">
                       <Button
                         variant="ghost"
                         size="icon"
                         onClick={() => removeRow(row.id)}
                         className="h-9 w-9"
                       >
                         <Trash2 className="h-4 w-4" />
                       </Button>
                     </div>
                      {/* Description (Full width below) */}
                     <div className="col-span-12 pt-1">
                        <Label htmlFor={`description-${row.id}`} className="sr-only">Description</Label>
                       <Input
                         id={`description-${row.id}`}
                         placeholder="Description (optional)"
                         value={row.description}
                         onChange={(e) => handleRowChange(row.id, 'description', e.target.value)}
                       />
                     </div>
                  </div>
                  {/* Removed Separator */}
                 </div>
              ))}
            </div>

            <div className="flex flex-col gap-4 mt-6">
              <Button
                variant="outline"
                className="w-full"
                onClick={addNewRow}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Row
              </Button>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setRows([createEmptyRow()])}>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Clear All
                </Button>
                <Button onClick={handleSubmit} disabled={isSubmitting}>
                  <Save className="mr-2 h-4 w-4" />
                  Save All
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    // Layout wrapper removed
  );
};

export default BatchAddItems; 
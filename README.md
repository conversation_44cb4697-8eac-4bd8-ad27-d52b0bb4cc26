# Imbentory - Inventory Management System

A modern, efficient inventory management system built with React, TypeScript, and Vite, packaged as a cross-platform desktop application using Tauri. Designed to help you track your inventory items across multiple locations with a beautiful user interface.

![Version](https://img.shields.io/badge/version-1.0.1-blue)
![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey)
![License](https://img.shields.io/badge/license-MIT-green)

## Architecture Overview

Imbentory is a cross-platform desktop application built with a modern tech stack that combines web technologies for the frontend and Rust for the backend, packaged using Tauri.

### High-Level Architecture

1. **Frontend (React + TypeScript)**
   - **UI Layer**: React components using Shadcn UI and TailwindCSS
   - **State Management**: React Context API for global state (Auth, Inventory)
   - **Data Fetching**: React Query for efficient data operations
   - **Routing**: React Router for navigation between screens
   - **Form Handling**: React Hook Form with Zod validation

2. **Backend (Tauri + Rust)**
   - **Core**: Tauri framework providing desktop capabilities
   - **Storage**: Local file system access for persistent data storage
   - **Security**: Rust-based security features and sandboxing
   - **Native Features**: Access to OS-level functionality when needed

3. **Data Flow**
   - User interactions trigger state changes in React components
   - Context providers (AuthContext, InventoryContext) manage global application state
   - Data is persisted locally using browser storage APIs and compressed with LZ-string
   - Authentication and data validation happen client-side

4. **Key Integration Points**
   - **Frontend ↔ Backend**: Tauri's JS API for communication between React and Rust
   - **Data Storage**: Local storage utilities for saving/loading application data
   - **PDF Generation**: React-PDF for report generation
   - **Theme System**: Integration between TailwindCSS and application preferences

5. **Module Structure**
   - **Components**: Reusable UI elements and page-specific components
   - **Context**: Global state management for auth and inventory
   - **Pages**: Screen-level components representing different views
   - **Utils**: Helper functions for storage, authentication, and data processing
   - **Types**: TypeScript interfaces defining data structures
   - **Hooks**: Custom React hooks for shared functionality

## Features

- **Item Management**
  - Add, edit, and delete items with detailed information
  - Track quantities, units, and expiration dates
  - Organize items by location
  - Batch operations for multiple items at once
  - Set low stock alerts for critical items

- **Location Management**
  - Create and manage multiple storage locations
  - View items by location
  - Detailed location-based inventory reports

- **Exports & Documentation**
  - Export inventory data to JSON, CSV, and PDF
  - Customizable PDF reports for printing
  - Data backup and restore capabilities

- **User Experience**
  - Modern, responsive UI with dark/light theme support
  - Customizable color themes
  - Quick search and filtering options

- **Performance & Storage**
  - Data compression for efficient storage usage
  - Storage usage monitoring and cleanup tools
  - Performance testing tools for large inventories
  - Efficient data structures and optimizations

## System Requirements

- Windows, macOS, or Linux Operating System
- (Tauri handles specific dependencies like WebView2 on Windows during installation)

## Tech Stack

- **Desktop Framework**: Tauri (Rust-based, for secure and fast desktop applications)
- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite (for fast development and optimized builds)
- **Styling**: TailwindCSS (utility-first CSS framework)
- **UI Components**: Shadcn UI (built on Radix UI) for accessible and beautiful components
- **State Management**: React Context API, React Query
- **Form Handling**: React Hook Form, Zod validation
- **PDF Generation**: React PDF
- **Data Compression**: LZ-string
- **Authentication**: bcryptjs
- **Charting**: Recharts
- **Routing**: React Router DOM
- **Navigation**: React Router for page routing
- **State Management**: React Context API for global state and React Query for data fetching
- **PDF Generation**: React PDF for customizable reports
- **Data Storage**: Local application storage (managed by Tauri) with LZ-string compression
- **Form Handling**: React Hook Form with Zod validation
- **Authentication**: Client-side authentication with password hashing using bcryptjs

## For Users

### Installation & Setup

1. Download the appropriate installer (`.msi` for Windows, `.dmg` for macOS, `.deb`/`.AppImage` for Linux) from the releases page.
2. Run the installer and follow the on-screen instructions.
3. Launch the "My Imbentory" application from your start menu or application folder.
4. On first launch, if no users exist, an admin account will be automatically created (see [Admin Account Setup](#admin-account-setup)).
5. Log in with the default admin credentials (or the ones configured during build).
6. Change the default password immediately for security reasons.

### Data Storage

- All data is stored locally on your computer within the application's designated data directory.
- Data is compressed to optimize storage space.
- Regular backups are recommended:
  - Use JSON export for complete system backups.
  - Use CSV export for spreadsheet compatibility.
  - Use PDF export for printable inventory reports.
- Data can be restored using the Import function in Settings.

## For Developers

### Prerequisites

- Node.js (LTS version recommended)
- Rust Toolchain (Install via [rustup.rs](https://rustup.rs/))
- System dependencies required by Tauri (see [Tauri Prerequisites Guide](https://tauri.app/v1/guides/getting-started/prerequisites))
- System dependencies required by Tauri (See [Tauri Prerequisites Guide](https://tauri.app/v1/guides/getting-started/prerequisites))

### Project Structure

```
imbentorys/
├── .docs/                  # Documentation and guides
├── .gitignore              # Git ignore rules
├── README.md               # Project documentation
├── cleanup-build.js        # Build cleanup script
├── components.json         # UI components config
├── dist/                   # Production build output
├── eslint.config.js        # ESLint configuration
├── index.html              # Main HTML file
├── lint-fix.js             # Lint fix script
├── node_modules/           # Node.js dependencies
├── package.json            # Node.js project configuration
├── package-lock.json       # NPM lock file
├── postcss.config.js       # PostCSS configuration
├── prepare-production.js   # Production prep script
├── public/                 # Static assets
├── rebuild.js              # Rebuild script
├── rebuild.sh              # Rebuild shell script
├── src/                    # Frontend React app
│   ├── App.tsx             # Main React app component
│   ├── components/         # UI components (items-table/, layout/, settings/, ui/)
│   ├── config/             # App configuration
│   ├── constants/          # App constants
│   ├── context/            # React context providers
│   ├── data/               # Static/sample data
│   ├── hooks/              # Custom React hooks
│   ├── index.css           # Global CSS
│   ├── lib/                # Third-party integrations
│   ├── main.tsx            # App entry point
│   ├── pages/              # Page-level components
│   ├── styles/             # Additional styles
│   ├── types/              # TypeScript types
│   ├── utils/              # Utility/helper functions
│   └── vite-env.d.ts       # Vite env types
├── src-tauri/              # Tauri (Rust) backend
│   ├── .gitignore
│   ├── Cargo.lock
│   ├── Cargo.toml
│   ├── build.rs
│   ├── capabilities/       # Tauri capabilities
│   ├── gen/                # Generated files
│   ├── icons/              # App icons
│   ├── src/                # Rust source
│   │   ├── lib.rs
│   │   └── main.rs
│   ├── target/             # Rust build output
│   └── tauri.conf.json     # Tauri config
├── tailwind.config.ts      # TailwindCSS config
├── tsconfig.app.json       # TS config (app)
├── tsconfig.json           # TS config (base)
├── tsconfig.node.json      # TS config (node)
├── vite.config.ts          # Vite config
```


### Development Commands

```bash
# Install frontend dependencies
npm install

# Start development environment (runs Vite dev server and Tauri app)
npm run tauri:dev

# Build frontend for production
npm run build

# Build the Tauri desktop app
npm run tauri:build

# Lint frontend code
npm run lint

# Fix linting issues automatically
npm run lint:fix

# Clean up previous build files and temporary Tauri files
npm run cleanup

# Build the application for production (with linting and auto-fix)
npm run prod:prepare

# Clean and build in one step (recommended for production)
npm run prod:clean-build
```

## Admin Account Setup

On first launch, if no users exist, the application automatically creates an admin account with the following default credentials:

- **Username**: admin
- **Password**: admin123!

These default credentials can be configured before building the application by editing `src/config/admin.config.ts`. For security reasons, it's recommended to change the default password after the first login.

### User Management

The application supports multiple user accounts with different permission levels:

- **Admin**: Full access to all features, including user management
- **User**: Access to inventory management features, but not administrative functions

## Key Application Features

- **Routing**: The application uses React Router for navigation between screens within the Tauri window.
- **Context Providers**: State management is handled through React Context API and React Query.
- **Theme Support**: Light/dark mode and customizable color themes.
- **Data Persistence**: All data is stored locally using browser storage APIs with LZ-string compression.
- **PDF Generation**: Custom PDF reports for inventory documentation.
- **Performance Testing**: Tools to test application performance with large datasets.
- **Data Compression**: LZ-string compression for efficient storage usage.
- **Optimized Rendering**: React optimizations for smooth UI performance even with large inventories.

## Detailed Architecture

### Data Flow Architecture

1. **User Interface Layer**
   - React components render the UI and capture user interactions
   - Components are organized by feature (items, locations, settings)
   - Shadcn UI provides consistent design patterns and accessibility

2. **Application State Layer**
   - **AuthContext**: Manages user authentication state
     - Handles login/logout operations
     - Stores current user information
     - Controls access to protected routes
   - **InventoryContext**: Manages inventory data
     - Provides CRUD operations for items and locations
     - Handles data validation and business logic
     - Manages relationships between items and locations

3. **Data Persistence Layer**
   - **Storage Utilities**: Helper functions for data operations
     - Compress data with LZ-string before storage
     - Store data in browser's localStorage
     - Handle data migrations and versioning
   - **Import/Export**: Functions for data portability
     - JSON format for complete data backup/restore
     - CSV format for spreadsheet compatibility
     - PDF format for printable reports

4. **Authentication System**
   - Client-side authentication with password hashing
   - Role-based access control (admin vs. regular users)
   - Password reset functionality with token-based verification
   - User approval workflow for new registrations

### Component Architecture

- **Layout Components**: Provide consistent structure across pages
  - Header with navigation and user controls
  - Sidebar for main navigation
  - Content area for page-specific components

- **Page Components**: Screen-level components with specific functionality
  - Dashboard for overview and quick access
  - Item management screens (list, add, edit, detail)
  - Location management screens
  - Settings and administration screens

- **Shared Components**: Reusable UI elements
  - Data tables with sorting and filtering
  - Form components with validation
  - Modal dialogs and notifications
  - PDF preview and generation

### Security Architecture

- **Authentication**: Client-side with bcryptjs for password hashing
- **Authorization**: Role-based access control for admin functions
- **Data Protection**: All data stored locally on user's device
- **Session Management**: Session persistence with secure storage

### Performance Optimizations

- **Data Compression**: Reduces storage footprint
- **Memoization**: Prevents unnecessary re-renders
- **Virtualization**: Efficient rendering of large lists
- **Lazy Loading**: Components loaded only when needed
- **Batch Operations**: Efficient handling of multiple items

## Troubleshooting

If you encounter any issues:

1. Ensure all developer prerequisites are installed correctly.
2. Try restarting the application.
3. Check the application logs (location varies by OS, often found in standard app data directories).
4. Consult the Tauri documentation for common issues.
5. Check the browser's console within the Tauri window (usually accessible via right-click -> Inspect Element if enabled in `tauri.conf.json`) for frontend errors.
6. Try the Storage Cleanup option in Settings if you suspect data-related issues.

## Performance and Optimization

The application is optimized for performance in several ways:

- **Data Compression**: All stored data is compressed using LZ-string to minimize storage usage
- **Lazy Loading**: Components and routes are loaded only when needed
- **Memoization**: React's useMemo and useCallback are used extensively to prevent unnecessary re-renders
- **Virtualization**: Large lists use virtualization to render only visible items
- **Storage Monitoring**: Built-in tools to monitor and manage storage usage
- **Performance Metrics**: Development tools to measure and optimize performance

## Security

- All data is stored locally on the user's device
- User passwords are securely hashed using industry-standard algorithms
- Admin account is created automatically on first launch with configurable credentials
- No data is sent to external servers
- Role-based access control for different user types

## Support and Contributions

For support, bug reports, or feature requests, please create an issue in the project repository.

## License

MIT License

## Acknowledgements

- [Tauri](https://tauri.app/) - Desktop application framework
- [React](https://reactjs.org/) - UI framework
- [Shadcn UI](https://ui.shadcn.com/) - UI components
- [TailwindCSS](https://tailwindcss.com/) - Styling system
- [React Query](https://tanstack.com/query/latest) - Data fetching and caching
- [React Hook Form](https://react-hook-form.com/) - Form management
- [Zod](https://zod.dev/) - Schema validation
- [LZ-string](https://pieroxy.net/blog/pages/lz-string/index.html) - Data compression
- [bcryptjs](https://github.com/dcodeIO/bcrypt.js/) - Password hashing
- [Recharts](https://recharts.org/) - Charting

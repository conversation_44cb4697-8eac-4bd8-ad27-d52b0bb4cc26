import { useEffect } from 'react';

/**
 * Custom hook that manages scroll position restoration
 * 
 * This hook saves the current scroll position to session storage
 * before navigating away, and restores it when returning to the page.
 * 
 * @param {string} storageKey - Unique key to identify the scroll position in session storage
 * @param {HTMLElement | null} ref - Optional ref to the scrollable element (uses window if not provided)
 */
export function useScrollRestoration(storageKey: string, ref?: React.RefObject<HTMLElement>) {
  useEffect(() => {
    const element = ref?.current ?? window;
    
    // Get stored position or default to top
    const savedPosition = sessionStorage.getItem(`scroll-${storageKey}`);
    const scrollPos = savedPosition ? parseInt(savedPosition, 10) : 0;
    
    // Restore scroll position
    if (ref?.current) {
      ref.current.scrollTop = scrollPos;
    } else {
      window.scrollTo(0, scrollPos);
    }
    
    // Save scroll position on unmount/key change
    const handleScroll = () => {
      const currentPos = ref?.current?.scrollTop ?? window.scrollY;
      sessionStorage.setItem(`scroll-${storageKey}`, currentPos.toString());
    };
    
    element.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      element.removeEventListener('scroll', handleScroll);
      handleScroll(); // Save position on unmount
    };
  }, [storageKey, ref]);
} 
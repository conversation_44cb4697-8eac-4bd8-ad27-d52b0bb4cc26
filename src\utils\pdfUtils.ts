/**
 * pdfUtils.ts
 * PDF generation and handling utility functions.
 * Uses react-pdf for PDF generation.
 */

import { Item, Location } from '@/types';
import { StyleSheet } from '@react-pdf/renderer';
import { formatWithTimezone } from './dateUtils';
import { APP_NAME } from '@/constants/app';

// Define styles for PDF documents
const styles = StyleSheet.create({
  page: {
    padding: 30,
    backgroundColor: '#ffffff'
  },
  section: {
    marginBottom: 10
  },
  header: {
    fontSize: 24,
    marginBottom: 20,
    fontWeight: 'bold',
    textAlign: 'center'
  },
  subheader: {
    fontSize: 18,
    marginBottom: 10,
    fontWeight: 'bold'
  },
  table: {
    display: 'flex',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#bfbfbf',
    marginBottom: 20
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#bfbfbf',
    borderBottomStyle: 'solid'
  },
  tableRowHeader: {
    backgroundColor: '#f0f0f0'
  },
  tableCol: {
    padding: 5
  },
  tableColHeader: {
    fontWeight: 'bold'
  },
  tableCell: {
    fontSize: 10
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    fontSize: 8,
    textAlign: 'center',
    color: '#666'
  },
  flexRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10
  },
  meta: {
    fontSize: 10,
    color: '#666',
    marginBottom: 10
  }
});

/**
 * Generates column widths for a table based on content
 * @param columns - Column definitions
 * @returns Object with column widths
 */
export const generateColumnWidths = (columns: { id: string; width?: number }[]) => {
  const totalDefinedWidth = columns.reduce((sum, col) => sum + (col.width || 0), 0);
  const remainingWidth = 100 - totalDefinedWidth;
  const countOfColumnsWithoutWidth = columns.filter(col => !col.width).length;

  const widths: Record<string, string> = {};
  columns.forEach(col => {
    if (col.width) {
      widths[col.id] = `${col.width}%`;
    } else if (countOfColumnsWithoutWidth > 0) {
      widths[col.id] = `${remainingWidth / countOfColumnsWithoutWidth}%`;
    }
  });

  return widths;
};

/**
 * Formats data for a location PDF export
 * @param location - The location to format
 * @param items - Items in the location
 * @returns Formatted data for PDF
 */
export const formatLocationPdfData = (location: Location, items: Item[]) => {
  return {
    locationName: location.name,
    locationDescription: location.description || 'No description',
    itemCount: items.length,
    generatedAt: new Date().toISOString(),
    items: items.map(item => ({
      name: item.name,
      quantity: item.quantity,
      unit: item.unit,
      description: item.description || 'No description',
      expiryDate: item.expiryDate ? formatWithTimezone(item.expiryDate) : 'No expiry date',
      watchStock: item.watchStock ? 'Yes' : 'No'
    }))
  };
};

/**
 * Get options for location PDF creation
 * @param location - The location to create a PDF for
 * @param items - Items in the location
 * @returns Configuration for PDF generation
 */
export const getLocationPdfOptions = (location: Location, items: Item[]) => {
  const data = formatLocationPdfData(location, items);
  const columns = [
    { id: 'name', name: 'Name', width: 25 },
    { id: 'quantity', name: 'Qty', width: 10 },
    { id: 'unit', name: 'Unit', width: 10 },
    { id: 'description', name: 'Description', width: 30 },
    { id: 'expiryDate', name: 'Expiry Date', width: 15 },
    { id: 'watchStock', name: 'Watch Stock', width: 10 }
  ];

  return {
    title: `${location.name} - ${APP_NAME}`,
    data,
    columns,
    styles,
    columnWidths: generateColumnWidths(columns)
  };
};
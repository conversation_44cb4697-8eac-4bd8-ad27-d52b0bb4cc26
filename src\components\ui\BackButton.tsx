import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BackButtonProps {
  /**
   * Optional custom label for the button (default: "Back")
   */
  label?: string;
  
  /**
   * Optional custom className to apply additional styles
   */
  className?: string;
  
  /**
   * Optional callback to execute when clicking the button
   * If not provided, it will use the default navigate(-1) behavior
   */
  onClick?: () => void;
}

/**
 * A reusable back button component with consistent styling
 */
const BackButton: React.FC<BackButtonProps> = ({ 
  label = "Back", 
  className = "",
  onClick
}) => {
  const navigate = useNavigate();
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(-1);
    }
  };
  
  return (
    <Button 
      variant="outline" 
      className={`mb-4 ${className}`}
      onClick={handleClick}
    >
      <ArrowLeft className="mr-2 h-4 w-4" />
      {label}
    </Button>
  );
};

export default BackButton; 
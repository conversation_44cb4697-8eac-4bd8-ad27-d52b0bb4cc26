import { toast } from "sonner";
import { Item, Location, User } from "@/types";
import { compressToUTF16, decompressFromUTF16 } from "lz-string";

// Constants
const STORAGE_KEYS = {
  ITEMS: 'inventory-items', // Key for storing items in localStorage
  LOCATIONS: 'inventory-locations', // Key for storing locations in localStorage
  SETTINGS: 'inventory-settings',  // Key for storing settings in localStorage
  USERS: 'inventory-users' // Key for storing users in localStorage
} as const;

const STORAGE_LIMITS = {
  WARNING_THRESHOLD: 0.8, // 80% of max storage - threshold to show storage warning
  MAX_SIZE: 5 * 1024 * 1024 // 5MB (typical localStorage limit) - maximum storage size
} as const;

/**
 * Calculates the current storage usage
 */
export const getStorageUsage = (): { used: number; total: number; percentage: number } => {
  let totalSize = 0;
  for (const key in localStorage) {
    if (Object.prototype.hasOwnProperty.call(localStorage, key)) { // Safer check for own properties
      totalSize += localStorage[key].length * 2; // UTF-16 characters are 2 bytes each
    }
  }

  return {
    used: totalSize,
    total: STORAGE_LIMITS.MAX_SIZE,
    percentage: (totalSize / STORAGE_LIMITS.MAX_SIZE) * 100
  };
};

/**
 * Checks if we're approaching storage limits
 */
export const checkStorageLimit = () => {
  const { percentage } = getStorageUsage();

  if (percentage >= STORAGE_LIMITS.WARNING_THRESHOLD * 100) {
    toast.error("Storage Warning", {
      description: `Your storage is ${Math.round(percentage)}% full. Consider exporting and clearing some data.`
    });
    return false;
  }

  return true;
};

/**
 * Cleans up old or invalid data from storage
 */
export const cleanupStorage = async (): Promise<void> => {
  try {
    // Load all data
    const items = loadItems();
    const locations = loadLocations();

    // Remove items with invalid or non-existent locations
    const validItems = items.filter(item =>
      locations.some(location => location.id === item.locationId)
    );

    // Remove empty descriptions
    const cleanedItems = validItems.map(item => ({
      ...item,
      description: item.description?.trim() || undefined
    }));

    const cleanedLocations = locations.map(location => ({
      ...location,
      description: location.description?.trim() || undefined
    }));

    // Save cleaned data
    if (items.length !== cleanedItems.length) {
      await saveItems(cleanedItems);
      toast.success("Storage Cleaned", {
        description: `Removed ${items.length - cleanedItems.length} invalid items.`
      });
    }

    await saveLocations(cleanedLocations);

  } catch (error) {
    console.error('Failed to cleanup storage:', error);
    toast.error("Cleanup Failed", {
      description: "Failed to cleanup storage. Please try again."
    });
  }
};

/**
 * Removes all empty or undefined values from an object
 */
const removeEmptyValues = <T extends Record<string, unknown>>(obj: T): Partial<T> => {
  const result: Partial<T> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (value !== null && value !== undefined && value !== '') {
      result[key as keyof T] = value as T[keyof T];
    }
  }

  return result;
};

/**
 * Optimizes item data for storage by removing unnecessary fields
 */
const optimizeItemData = (item: Item): Partial<Item> =>
  removeEmptyValues({
    id: item.id,
    name: item.name,
    quantity: item.quantity,
    unit: item.unit,
    locationId: item.locationId,
    description: item.description,
    expiryDate: item.expiryDate,
    watchStock: item.watchStock,
    createdAt: item.createdAt,
    updatedAt: item.updatedAt,
    createdByUserId: item.createdByUserId, // Add createdByUserId
    updatedByUserId: item.updatedByUserId  // Add updatedByUserId
  });

/**
 * Optimizes location data for storage
 */
const optimizeLocationData = (location: Location): Partial<Location> =>
  removeEmptyValues({
    id: location.id,
    name: location.name,
    description: location.description,
    color: location.color, // Add the color field here
    createdAt: location.createdAt,
    updatedAt: location.updatedAt,
    createdByUserId: location.createdByUserId,
    updatedByUserId: location.updatedByUserId
  });

/**
 * Compresses data for storage
 */
const compressData = (data: unknown): string => {
  try {
    const jsonString = JSON.stringify(data);
    return compressToUTF16(jsonString);
  } catch (error) {
    console.error('Failed to compress data:', error);
    return JSON.stringify(data);
  }
};

/**
 * Decompresses data from storage
 */
const decompressData = <T>(compressedData: string): T | null => {
  try {
    const jsonString = decompressFromUTF16(compressedData);
    return jsonString ? JSON.parse(jsonString) : null;
  } catch (error) {
    // If decompression fails, try parsing as regular JSON
    try {
      return JSON.parse(compressedData);
    } catch {
      console.error('Failed to decompress data:', error);
      return null;
    }
  }
};

/**
 * Processes items in batches to avoid blocking the main thread
 */
const processBatch = async <T>(
  items: T[],
  batchSize: number,
  processFn: (batch: T[]) => void
): Promise<void> => {
  const batches = Math.ceil(items.length / batchSize);

  for (let i = 0; i < batches; i++) {
    const start = i * batchSize;
    const end = Math.min(start + batchSize, items.length);
    const batch = items.slice(start, end);

    // Process batch
    processFn(batch);

    // Allow UI to update between batches
    if (i < batches - 1) {
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }
};

/**
 * Saves items to localStorage with optimization, compression, and batch processing
 */
export const saveItems = async (items: Item[]): Promise<boolean> => {
  try {
    if (!checkStorageLimit()) return false;

    const optimizedItems: Partial<Item>[] = [];
    await processBatch(items, 100, (batch) => {
      optimizedItems.push(...batch.map(optimizeItemData));
    });

    const compressedData = compressData(optimizedItems);
    localStorage.setItem(STORAGE_KEYS.ITEMS, compressedData);
    return true;
  } catch (error) {
    console.error('Failed to save items:', error);
    toast.error("Error Saving Data", {
      description: "Failed to save your items. Please check your storage usage."
    });
    return false;
  }
};

/**
 * Saves locations to localStorage with optimization, compression, and batch processing
 */
export const saveLocations = async (locations: Location[]): Promise<boolean> => {
  try {
    if (!checkStorageLimit()) return false;

    const optimizedLocations: Partial<Location>[] = [];
    await processBatch(locations, 100, (batch) => {
      optimizedLocations.push(...batch.map(optimizeLocationData));
    });

    const compressedData = compressData(optimizedLocations);
    localStorage.setItem(STORAGE_KEYS.LOCATIONS, compressedData);
    return true;
  } catch (error) {
    console.error('Failed to save locations:', error);
    toast.error("Error Saving Data", {
      description: "Failed to save your locations. Please check your storage usage."
    });
    return false;
  }
};

/**
 * Loads items from localStorage with decompression
 */
export const loadItems = (): Item[] => {
  try {
    const savedItems = localStorage.getItem(STORAGE_KEYS.ITEMS);
    if (!savedItems) return [];

    const decompressedItems = decompressData<Item[]>(savedItems);
    return decompressedItems || [];
  } catch (error) {
    console.error('Failed to load items:', error);
    toast.error("Error Loading Data", {
      description: "Failed to load your items. Data might be corrupted."
    });
    return [];
  }
};

/**
 * Loads locations from localStorage with decompression
 */
export const loadLocations = (): Location[] => {
  try {
    const savedLocations = localStorage.getItem(STORAGE_KEYS.LOCATIONS);
    if (!savedLocations) return [];

    const decompressedLocations = decompressData<Location[]>(savedLocations);
    return decompressedLocations || [];
  } catch (error) {
    console.error('Failed to load locations:', error);
    toast.error("Error Loading Data", {
      description: "Failed to load your locations. Data might be corrupted."
    });
    return [];
  }
};

/**
 * Saves users to localStorage with compression
 */
export const saveUsers = async (users: User[]): Promise<boolean> => {
  try {
    // Note: No optimization step needed for simple user structure yet
    const compressedData = compressData(users);
    localStorage.setItem(STORAGE_KEYS.USERS, compressedData);
    // Consider storage limit check if user data becomes large
    return true;
  } catch (error) {
    console.error('Failed to save users:', error);
    toast.error("Error Saving Data", {
      description: "Failed to save user accounts."
    });
    return false;
  }
};

/**
 * Loads users from localStorage with decompression
 */
export const loadUsers = (): User[] => {
  try {
    const savedUsers = localStorage.getItem(STORAGE_KEYS.USERS);
    if (!savedUsers) return [];

    const decompressedUsers = decompressData<User[]>(savedUsers);
    return decompressedUsers || [];
  } catch (error) {
    console.error('Failed to load users:', error);
    toast.error("Error Loading Data", {
      description: "Failed to load user accounts. Data might be corrupted."
    });
    return [];
  }
};

/**
 * Clears inventory data from localStorage but preserves user data
 */
export const clearStorage = () => {
  try {
    localStorage.removeItem(STORAGE_KEYS.ITEMS);
    localStorage.removeItem(STORAGE_KEYS.LOCATIONS);
    // Do NOT remove users data
    // localStorage.removeItem(STORAGE_KEYS.USERS);
    return true;
  } catch (error) {
    console.error('Failed to clear storage:', error);
    return false;
  }
};
Code Quality & Style:
Write clean, maintainable code that prioritizes simplicity and clarity while following industry best practices for the current tech stack. If you encounter uncertainty in implementation, request clarification rather than making assumptions. All code should be properly commented and structured for long-term maintainability.

TypeScript Type Safety:
Avoid using the any type in TypeScript code. Instead, define proper interfaces, types, or use generics. When dealing with unknown data structures, use unknown type and implement proper type guards. If any is absolutely necessary, document the reason with a comment and isolate its usage to the smallest possible scope. Consider using eslint-plugin-typescript-strict to enforce strict typing rules.

UI Implementation:
When implementing UI components, strictly adhere to Tailwind and Radix/Shadcn UI defaults without introducing hardcoded styles. Utilize existing component libraries and follow their established patterns. Custom styling should only be applied through the design system's approved methods.

Data & Production Standards:
Production code should be free of mock data, todos, placeholders, or sample data unless explicitly approved. All data structures and implementations should be production-ready and properly validated. Test data must be clearly separated from production code.

Debugging Methodology:
When troubleshooting issues, first analyze multiple potential root causes, narrow down to the most probable sources, and implement strategic logging to validate your hypotheses. Always seek confirmation of the diagnosis before proceeding with fixes to ensure accurate problem resolution. Document any debugging steps and findings for future reference.
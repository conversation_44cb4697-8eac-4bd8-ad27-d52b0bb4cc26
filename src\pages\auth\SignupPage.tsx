import React, { useState, useCallback } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { BookHeart } from 'lucide-react';
import { cn } from '@/lib/utils'; // Utility for conditional class name merging
import { toast } from 'sonner';
import { Link, useNavigate } from 'react-router-dom';

/**
 * SignupPage Component
 *
 * Renders the registration form for new users. Handles input validation, user creation,
 * displays error/feedback messages, and redirects to login on success.
 */
const SignupPage: React.FC = () => {
  const { registerNewUser } = useAuth();
  const navigate = useNavigate();

  // --- State ---
  // Local state for form fields and feedback
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInvalid, setIsInvalid] = useState(false);

  // --- Handlers ---
  // Each input handler updates its field and clears error state if needed
  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUsername(e.target.value);
    if (isInvalid) {
      setIsInvalid(false);
      setError(null);
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (isInvalid) {
      setIsInvalid(false);
      setError(null);
    }
  };

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(e.target.value);
    if (isInvalid) {
      setIsInvalid(false);
      setError(null);
    }
  };

  const handleFullNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFullName(e.target.value);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (isInvalid) {
      setIsInvalid(false);
      setError(null);
    }
  };

  /**
   * Handles registration form submission:
   * - Validates all fields (username, email, password, confirmPassword)
   * - Shows error messages for invalid input
   * - Calls registerNewUser and handles registration result
   * - Shows toasts and redirects on success
   */
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // --- Validation ---
    setError(null);
    setIsInvalid(false);

    if (!username.trim()) {
      setError("Username is required");
      setIsInvalid(true);
      return;
    }
    if (!email.trim()) {
      setError("Email is required");
      setIsInvalid(true);
      return;
    }
    if (!password) {
      setError("Password is required");
      setIsInvalid(true);
      return;
    }
    if (password.length < 6) {
      setError("Password must be at least 6 characters");
      setIsInvalid(true);
      return;
    }
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setIsInvalid(true);
      return;
    }

    setIsSubmitting(true);

    try {
      const success = await registerNewUser({
        username: username.trim(),
        password,
        fullName: fullName.trim() || undefined,
        email: email.trim()
      });

      if (success) {
        toast.success("Registration Successful", {
          description: "Your account has been created and is pending admin approval."
        });
        navigate('/login');
      } else {
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error("Registration error:", error);
      setError("An unexpected error occurred. Please try again.");
      setIsInvalid(true);
      setIsSubmitting(false);
    }
  }, [username, password, confirmPassword, fullName, email, registerNewUser, navigate]);

  // --- Rendering ---
  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-md space-y-8">
        {/* App Title and Description Section */}
        <div className="flex flex-col items-center text-center">
          <BookHeart className="h-12 w-12 text-primary" />
          <h1 className="mt-2 text-2xl font-bold">Imbentorys</h1>
          <p className="text-sm text-muted-foreground">Create a new account</p>
        </div>

        {/* Registration Form Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Sign Up</CardTitle>
            <CardDescription>Enter your details to create a new account</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username*</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="Enter a username"
                  value={username}
                  onChange={handleUsernameChange}
                  required
                  aria-invalid={isInvalid}
                  className={cn(isInvalid && "border-destructive focus-visible:ring-destructive")}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email*</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={handleEmailChange}
                  required
                  aria-invalid={isInvalid}
                  className={cn(isInvalid && "border-destructive focus-visible:ring-destructive")}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fullName">Name</Label>
                <Input
                  id="fullName"
                  type="text"
                  placeholder="Enter your full name"
                  value={fullName}
                  onChange={handleFullNameChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password*</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Create a password (min. 6 characters)"
                  value={password}
                  onChange={handlePasswordChange}
                  required
                  aria-invalid={isInvalid}
                  className={cn(isInvalid && "border-destructive focus-visible:ring-destructive")}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password*</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  required
                  aria-invalid={isInvalid}
                  className={cn(isInvalid && "border-destructive focus-visible:ring-destructive")}
                />
              </div>
              {/* Error message for invalid registration attempts */}
              {error && (
                <div className="bg-destructive/10 text-destructive rounded-md p-3 text-sm">
                  {error}
                </div>
              )}
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? 'Creating Account...' : 'Create Account'}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <p className="text-sm text-muted-foreground">
              Already have an account?{' '}
              <Link to="/login" className="text-primary hover:underline">
                Log in
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default SignupPage;

/**
 * Script management utility functions.
 */

/**
 * Safely loads a script with a given ID, ensuring no duplicates
 * @param id - The ID to assign to the script
 * @param src - The source URL of the script
 * @param async - Whether to load the script asynchronously
 * @returns Promise that resolves when the script is loaded
 */
export const loadScriptSafely = (
  id: string,
  src: string,
  async = true
): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if script with this ID already exists
    const existingScript = document.getElementById(id);
    if (existingScript) {
      console.log(`Script with ID ${id} already exists, skipping load`);
      resolve(); // Script already exists, resolve immediately
      return;
    }

    // Create and append the script
    const script = document.createElement('script');
    script.id = id;
    script.src = src;
    script.async = async;

    script.onload = () => {
      console.log(`Script ${id} loaded successfully`);
      resolve();
    };

    script.onerror = (error) => {
      console.error(`Error loading script ${id}:`, error);
      reject(error);
    };

    document.head.appendChild(script);
  });
};

/**
 * Removes a script with the given ID from the document
 * @param id - The ID of the script to remove
 */
export const removeScript = (id: string): void => {
  const script = document.getElementById(id);
  if (script) {
    script.remove();
    console.log(`Script ${id} removed`);
  }
};

/**
 * Prevents duplicate script ID errors by checking for and removing duplicates
 */
export const preventDuplicateScripts = (): void => {
  // List of script IDs to check for duplicates
  const scriptIds = ['fido2-page-script-registration'];

  scriptIds.forEach(id => {
    const elements = document.querySelectorAll(`#${id}`);
    if (elements.length > 1) {
      // Keep only the first instance, remove others
      for (let i = 1; i < elements.length; i++) {
        elements[i].remove();
      }
      console.log(`Removed ${elements.length - 1} duplicate script(s) with ID: ${id}`);
    }
  });
};

/**
 * Setup a mutation observer to prevent duplicate scripts after DOM changes
 * This helps with errors that appear after login/navigation
 */
export const setupScriptObserver = (): void => {
  // Create an observer instance
  const observer = new MutationObserver((mutations) => {
    // Check if any mutations added scripts
    let needsCheck = false;

    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any of the added nodes are scripts
        for (let i = 0; i < mutation.addedNodes.length; i++) {
          const node = mutation.addedNodes[i];
          if (node.nodeName === 'SCRIPT') {
            needsCheck = true;
            break;
          }
        }
        if (needsCheck) break;
      }
    }

    if (needsCheck) {
      // Re-check for duplicate scripts
      preventDuplicateScripts();
    }
  });

  // Start observing the document head for script additions
  observer.observe(document.head, {
    childList: true,
    subtree: true
  });

  // Also observe the body for scripts that might be added there
  observer.observe(document.body, { childList: true, subtree: true });
  // No return value; function type is void
};

/**
 * Handles runtime.lastError by suppressing the error message
 * This is useful for extension-related errors that can't be fixed
 */
export const suppressRuntimeErrors = (): void => {
  // Create a global error handler for extension port errors
  window.addEventListener('error', (event) => {
    // Check if the error is related to extension ports
    if (event.message && (
      event.message.includes('extension port') ||
      event.message.includes('message channel is closed') ||
      event.message.includes('runtime.lastError')
    )) {
      // Prevent the error from being displayed in the console
      event.preventDefault();
      // Optionally log a more friendly message
      console.debug('Suppressed extension port error:', event.message);
      return true;
    }
    return false;
  }, true);
};

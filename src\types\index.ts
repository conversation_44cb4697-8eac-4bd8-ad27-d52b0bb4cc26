export interface Item {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  locationId: string;
  description: string;
  expiryDate: string | null;
  watchStock: boolean;
  createdAt: string;
  updatedAt: string;
  createdByUserId?: string;
  updatedByUserId?: string;
}

export interface Location {
  id: string;
  name: string;
  description: string;
  color?: string;
  createdAt: string;
  updatedAt: string;
  createdByUserId?: string;
  updatedByUserId?: string;
}

export interface ValidationError {
  type: 'item' | 'location';
  name: string;
  error: string;
}

export interface User {
  id: string;
  username: string;
  passwordHash: string;
  role: 'admin' | 'user';
  status: 'pending' | 'active' | 'rejected' | 'password_reset';
  fullName?: string;
  email?: string;
  avatarUrl?: string;
  createdAt: string;
  updatedAt: string;
  resetToken?: string;
  resetTokenExpiry?: string;
}
import { useState, useEffect, useMemo } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { Save } from 'lucide-react';
import { useInventory } from '@/context/InventoryContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { handleUnitChange } from "@/utils/itemUtils";
import BackButton from '@/components/ui/BackButton';

interface EditableItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  description: string;
  expiryDate: string | null;
  watchStock: boolean;
  locationId: string;
}

const BatchEditItems = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { locationId } = useParams();
  const { items, updateItem, getLocationById, locations } = useInventory();
  const [editedItems, setEditedItems] = useState<Record<string, EditableItem>>({});
  const locationName = location.state?.locationName || '';

  // Memoize selected item IDs to avoid unnecessary recalculations
  const selectedItemIds = useMemo(() =>
    location.state?.selectedItems || [],
    [location.state?.selectedItems]
  );

  useEffect(() => {
    const itemsToEdit = selectedItemIds.reduce((acc, id) => {
      const item = items.find(i => i.id === id);
      if (item) {
        acc[id] = {
          id: item.id,
          name: item.name,
          quantity: item.quantity,
          unit: item.unit,
          description: item.description || '',
          expiryDate: item.expiryDate,
          watchStock: item.watchStock,
          locationId: item.locationId,
        };
      }
      return acc;
    }, {});
    setEditedItems(itemsToEdit);
  }, [selectedItemIds, items]);

  const handleItemChange = (itemId: string, field: keyof EditableItem, value: string | number | boolean) => {
    setEditedItems(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        [field]: value
      }
    }));
  };

  const handleItemUnitChange = (itemId: string) => handleUnitChange(
    (value) => handleItemChange(itemId, 'unit', value),
    (isCustom) => {
      if (isCustom) {
        const customUnit = prompt('Enter custom unit:');
        if (customUnit) {
          handleItemChange(itemId, 'unit', customUnit);
        }
      }
    }
  );

  const handleSave = () => {
    // Update all changed fields for each item
    Object.entries(editedItems).forEach(([itemId, changes]) => {
      updateItem(itemId, {
        name: changes.name,
        quantity: changes.quantity,
        unit: changes.unit,
        description: changes.description,
        expiryDate: changes.expiryDate,
        watchStock: changes.watchStock,
        locationId: changes.locationId
      });
    });

    toast.success("Items Updated", {
      description: `Successfully updated ${Object.keys(editedItems).length} items.`
    });

    // Navigate to the return location after saving
    if (locationId) {
      navigate(`/locations/${locationId}`);
    } else {
      navigate('/items');
    }
  };

  const existingUnits = Array.from(new Set(items.map(item => item.unit))).filter(Boolean);

  return (
    // Layout wrapper removed
      <div className="space-y-6 animate-slide-in">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">Batch Edit Items</h1>
            <p className="text-muted-foreground">
              {locationName
                ? `Editing ${Object.keys(editedItems).length} items in ${locationName}`
                : `Editing ${Object.keys(editedItems).length} items`
              }
            </p>
          </div>
          <div className="flex gap-2">
            <BackButton />
            <Button onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </div>

        <Card className="card">
          <CardHeader>
            <CardTitle>Edit Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.values(editedItems).map((item, index) => (
                // Apply border to the outer div, adjust padding
                <div key={item.id} className={`py-3 pt-0 ${index < Object.values(editedItems).length - 1 ? 'border-b border-border/80' : ''}`}>
                  <div className="grid grid-cols-12 gap-1 items-center">
                    {/* Name */}
                    <div className="col-span-12 sm:col-span-6 md:col-span-4">
                      <Label htmlFor={`name-${item.id}`} className="sr-only">Name</Label>
                      <Input
                        id={`name-${item.id}`}
                        placeholder="Item name"
                        value={item.name}
                        onChange={(e) => handleItemChange(item.id, 'name', e.target.value)}
                      />
                    </div>
                    {/* Quantity */}
                    <div className="col-span-4 sm:col-span-2 md:col-span-1">
                       <Label htmlFor={`quantity-${item.id}`} className="sr-only">Quantity</Label>
                      <Input
                        id={`quantity-${item.id}`}
                        placeholder="Qty"
                        type="number"
                        min="0"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(item.id, 'quantity', parseInt(e.target.value) || 0)}
                      />
                    </div>
                     {/* Unit */}
                    <div className="col-span-8 sm:col-span-4 md:col-span-2">
                       <Label htmlFor={`unit-${item.id}`} className="sr-only">Unit</Label>
                      <Select
                        value={item.unit}
                        onValueChange={(value) => handleItemUnitChange(item.id)(value)}
                      >
                        <SelectTrigger id={`unit-${item.id}`}>
                          <SelectValue placeholder="Unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {existingUnits.map(existingUnit => (
                            <SelectItem key={existingUnit} value={existingUnit}>
                              {existingUnit}
                            </SelectItem>
                          ))}
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                     {/* Location */}
                     <div className="col-span-6 sm:col-span-4 md:col-span-2">
                       <Label htmlFor={`location-${item.id}`} className="sr-only">Location</Label>
                       <Select
                         value={item.locationId}
                         onValueChange={(value) => handleItemChange(item.id, 'locationId', value)}
                       >
                         <SelectTrigger id={`location-${item.id}`}>
                           <SelectValue placeholder="Select location" />
                         </SelectTrigger>
                         <SelectContent>
                           {locations.map(loc => (
                             <SelectItem key={loc.id} value={loc.id}>
                               {loc.name}
                             </SelectItem>
                           ))}
                         </SelectContent>
                       </Select>
                    </div>
                     {/* Expiry Date */}
                     <div className="col-span-6 sm:col-span-3 md:col-span-2">
                        <Label htmlFor={`expiry-${item.id}`} className="sr-only">Expiry Date</Label>
                       <Input
                         id={`expiry-${item.id}`}
                         type="date"
                         value={item.expiryDate ? item.expiryDate.split('T')[0] : ''}
                         onChange={(e) => handleItemChange(
                           item.id,
                           'expiryDate',
                           e.target.value ? new Date(e.target.value).toISOString() : null
                         )}
                       />
                     </div>
                     {/* Description input will be added below */}
                      {/* Watch Stock */}
                     <div className="col-span-12 sm:col-span-6 md:col-span-1 flex items-center justify-start sm:justify-end pt-1 sm:pt-0">
                       <div className="flex items-center space-x-2">
                         <Checkbox
                           id={`watchStock-${item.id}`}
                           checked={item.watchStock}
                           onCheckedChange={(checked) =>
                             handleItemChange(item.id, 'watchStock', checked as boolean)
                           }
                         />
                         <Label htmlFor={`watchStock-${item.id}`} className="whitespace-nowrap">Watch</Label>
                       </div>
                     </div>
                     {/* Description (Full width below other fields) */}
                     <div className="col-span-12 pt-1">
                        <Label htmlFor={`description-${item.id}`} className="sr-only">Description</Label>
                       <Input
                         id={`description-${item.id}`}
                         placeholder="Description (optional)"
                         value={item.description}
                         onChange={(e) => handleItemChange(item.id, 'description', e.target.value)}
                       />
                     </div>
                  </div>
                 </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    // Layout wrapper removed
  );
};

export default BatchEditItems;
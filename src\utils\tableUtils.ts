/**
 * tableUtils.ts
 *
 * Table data and rendering utility functions.
 */

import { TABLE_DEFAULTS } from '@/constants/ui';
import { measurePerformance, monitorSortPerformance, monitorFilterPerformance } from './performanceUtils';

// Types for table operations
export type SortDirection = typeof TABLE_DEFAULTS.SORT_DIRECTIONS[number];
export type SortConfig<T> = {
  column: keyof T;
  direction: SortDirection;
};

export type FilterConfig<T> = {
  column: keyof T;
  value: string;
};

export type PaginationConfig = {
  currentPage: number;
  pageSize: number;
  totalItems: number;
};

/**
 * Sorts an array of items based on a column and direction
 * @param items - Array of items to sort
 * @param sortConfig - The sort configuration (column and direction)
 * @returns Sorted array of items
 */
export const sortData = <T extends Record<string, unknown>>(
  items: T[],
  sortConfig: SortConfig<T> | null
): T[] => {
  if (!sortConfig) return [...items];

  const sortPerformance = measurePerformance('sortData', () => {});

  const sorted = [...items].sort((a, b) => {
    if (a[sortConfig.column] === null || a[sortConfig.column] === undefined) return 1;
    if (b[sortConfig.column] === null || b[sortConfig.column] === undefined) return -1;

    const aValue = a[sortConfig.column];
    const bValue = b[sortConfig.column];

    // Handle different data types
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      // String comparison
      return sortConfig.direction === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    // Handle date strings
    if (
      typeof aValue === 'string' &&
      typeof bValue === 'string' &&
      !isNaN(Date.parse(aValue)) &&
      !isNaN(Date.parse(bValue))
    ) {
      // Date comparison
      return sortConfig.direction === 'asc'
        ? new Date(aValue).getTime() - new Date(bValue).getTime()
        : new Date(bValue).getTime() - new Date(aValue).getTime();
    }

    // Handle numbers and other types
    // Number or default comparison
    return sortConfig.direction === 'asc'
      ? aValue > bValue ? 1 : -1
      : aValue < bValue ? 1 : -1;
  });

  monitorSortPerformance(items, sortPerformance);
  return sorted;
};

/**
 * Filters an array of items based on search text and columns to search
 * @param items - Array of items to filter
 * @param searchText - Text to search for
 * @param columnsToSearch - Array of column keys to search in
 * @returns Filtered array of items
 */
export const filterData = <T extends Record<string, unknown>>(
  items: T[],
  searchText: string,
  columnsToSearch: (keyof T)[]
): T[] => {
  if (!searchText.trim()) return items;

  const filterPerformance = measurePerformance('filterData', () => {});
  const searchLower = searchText.toLowerCase();

  const filtered = items.filter(item =>
    columnsToSearch.some(column => {
      const value = item[column];
      if (value === null || value === undefined) return false;

      // Convert any type to string for searching
      const valueStr = String(value).toLowerCase();
      return valueStr.includes(searchLower);
    })
  );

  monitorFilterPerformance(items, filterPerformance);
  return filtered;
};

/**
 * Paginates an array of items
 * @param items - Array of items to paginate
 * @param currentPage - Current page (1-based)
 * @param pageSize - Number of items per page
 * @returns Paginated array of items
 */
export const paginateData = <T>(
  items: T[],
  currentPage: number,
  pageSize: number
): T[] => {
  const startIndex = (currentPage - 1) * pageSize;
  return items.slice(startIndex, startIndex + pageSize);
};

/**
 * Calculates pagination information
 * @param totalItems - Total number of items
 * @param currentPage - Current page (1-based)
 * @param pageSize - Number of items per page
 * @returns Pagination information
 */
export const calculatePagination = (
  totalItems: number,
  currentPage: number,
  pageSize: number
): PaginationConfig & { totalPages: number; hasNextPage: boolean; hasPrevPage: boolean } => {
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));
  const safePage = Math.min(Math.max(1, currentPage), totalPages);

  return {
    currentPage: safePage,
    pageSize,
    totalItems,
    totalPages,
    hasNextPage: safePage < totalPages,
    hasPrevPage: safePage > 1
  };
};
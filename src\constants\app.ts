/**
 * app.ts
 *
 * Application-wide constants and configuration values.
 * Includes settings, defaults, and global definitions for maintainability.
 * All app-level configuration should be defined here for consistency.
 */

// Application metadata
export const APP_NAME = 'Imbentory'; // The name of the application
export const APP_VERSION = '1.0.0'; // The current version of the application
export const APP_DESCRIPTION = 'Inventory Management System'; // A brief description of the application's purpose

// Theme settings
export const DEFAULT_THEME = 'light';
export const AVAILABLE_THEMES = ['light', 'dark', 'system'] as const;
export const COLOR_THEMES = ['gray', 'blue', 'orange', 'default'] as const;

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'theme',
  COLOR_ACCENT: 'color-accent',
  INVENTORY: 'inventory-data',
  LOCATIONS: 'location-data',
  SETTINGS: 'user-settings',
  LAST_VIEW: 'last-view'
};

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10;
export const AVAILABLE_PAGE_SIZES = [5, 10, 20, 50, 100];

// Date formats
export const DATE_FORMAT = {
  SHORT: 'MM/dd/yyyy',
  MEDIUM: 'MMM d, yyyy',
  LONG: 'MMMM d, yyyy',
  FULL: 'EEEE, MMMM d, yyyy'
};

// Route paths
export const ROUTES = {
  // Dashboard
  HOME: '/',

  // Locations
  LOCATIONS: '/locations',
  LOCATION_DETAIL: '/locations/:locationId',
  EDIT_LOCATION: '/locations/edit/:locationId',
  ADD_LOCATION: '/add-location',
  LOCATION_PDF: '/locations/:locationId/pdf',

  // Inventory
  ITEMS: '/items',
  ITEM_DETAIL: '/items/:itemId',
  ADD_ITEM: '/add-item',
  EDIT_ITEM: '/edit-item/:itemId',
  BATCH_ADD_ITEMS: '/batch-add-items',
  BATCH_EDIT_ITEMS: '/locations/:locationId/batch-edit',

  // Settings
  SETTINGS: '/settings',
  PERFORMANCE_TEST: '/performance-test',

  // Auth
  LOGIN: '/login',
  SIGNUP: '/signup',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  PASSWORD_RESET_PENDING: '/password-reset-pending',
  PENDING_APPROVAL: '/pending-approval',

  // Admin
  ADMIN: '/admin',
  ADMIN_PASSWORD_RESET: '/admin/password-reset',

  // Common
  NOT_FOUND: '*'
};
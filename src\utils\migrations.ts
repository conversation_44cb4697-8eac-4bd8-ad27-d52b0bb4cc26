import { User } from '@/types';
import { loadUsers, saveUsers } from './storageUtils';
import { toast } from 'sonner';

/**
 * Migrates existing users to include the status field.
 * This should be run once when the app starts.
 */
export const migrateUsers = async (): Promise<boolean> => {
  try {
    const users = loadUsers();
    let needsMigration = false;
    
    // Check if any user is missing the status field
    for (const user of users) {
      if (!('status' in user)) {
        needsMigration = true;
        break;
      }
    }
    
    if (needsMigration) {
      console.log('Migrating users to include status field...');
      
      // Update all users to include status field (default to 'active' for existing users)
      const migratedUsers = users.map(user => ({
        ...user,
        status: 'active' as const
      }));
      
      // Save the migrated users
      await saveUsers(migratedUsers);
      console.log('User migration completed successfully.');
      return true;
    }
    
    return true; // No migration needed
  } catch (error) {
    console.error('Failed to migrate users:', error);
    toast.error('Migration Error', {
      description: 'Failed to migrate user data. Some features may not work correctly.'
    });
    return false;
  }
};

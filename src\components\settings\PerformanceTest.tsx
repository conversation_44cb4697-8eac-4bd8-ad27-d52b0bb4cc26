import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useInventory } from '@/context/InventoryContext';
// useAuth import removed, context handles user ID
import { generateTestDataset } from '@/utils/testUtils';
import { generatePerformanceReport, markPerformanceEvent, measurePerformanceBetweenMarks } from '@/utils/performanceUtils';
import { AlertCircle, Package } from 'lucide-react';
import BackButton from '../ui/BackButton';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';

const PerformanceTest = () => {
  const { items, locations, addItem, addLocation, clearAllData } = useInventory();
  // user variable removed
  const [locationCount, setLocationCount] = useState('9');
  const [itemCount, setItemCount] = useState('500');
  const [isGenerating, setIsGenerating] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingGenerate, setPendingGenerate] = useState(false);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${result}`]);
  };

  // Opens a confirmation dialog before generating test data, to prevent accidental data loss.
  const handleGenerateData = () => {
    setShowConfirmDialog(true);
  };

  // Performs the actual test data generation after user confirmation.
  const doGenerateData = async () => {
    setShowConfirmDialog(false);
    setIsGenerating(true);
    addTestResult('Starting data generation...');

    try {
      // Clear existing inventory data (preserves user data)
      await clearAllData();
      addTestResult('Cleared existing inventory data (user data preserved)');

      // Mark start of data generation
      markPerformanceEvent('generateStart');

      // Generate test data
      const { locations: testLocations, items: testItems } = generateTestDataset(
        parseInt(locationCount),
        parseInt(itemCount)
      );

      // Add locations first
      addTestResult(`Adding ${testLocations.length} locations...`);
      for (const location of testLocations) {
        // Context will handle adding user ID
        await addLocation(location);
      }

      // Add items in batches
      addTestResult(`Adding ${testItems.length} items...`);
      const BATCH_SIZE = 100;
      for (let i = 0; i < testItems.length; i += BATCH_SIZE) {
        const batch = testItems.slice(i, i + BATCH_SIZE);
        // Context will handle adding user ID
        await Promise.all(batch.map(item => addItem(item)));
        addTestResult(`Added items ${i + 1} to ${Math.min(i + BATCH_SIZE, testItems.length)}`);
      }

      // Mark end of data generation
      markPerformanceEvent('generateEnd');

      // Measure total generation time
      const totalTime = measurePerformanceBetweenMarks('dataGeneration', 'generateStart', 'generateEnd');
      addTestResult(`Data generation completed in ${totalTime.toFixed(2)}ms`);

      // Generate performance report
      const report = generatePerformanceReport();
      addTestResult('Performance report generated (see console)');

    } catch (error) {
      addTestResult(`Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <>
      <div className="space-y-6 animate-slide-in">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Package className="h-7 w-7 text-primary" />
              Performance Test
            </h1>
            <p className="text-muted-foreground">
            Generate test inventory data for performance testing. 
            </p>
          </div>
        </div>
        <Card className="max-w-2xl mr-auto border border-destructive/50">
          <CardContent>
            <span className="text-destructive text-md flex items-center gap-2"><AlertCircle className="h-5 w-5" />This will clear existing inventory items and locations but preserve all user accounts.</span>
          </CardContent>
        </Card>
        <Card className="max-w-2xl mr-auto">
          <CardContent>
            <div className="grid gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="locationCount">Number of Locations</Label>
                  <Input
                    id="locationCount"
                    type="number"
                    value={locationCount}
                    onChange={e => setLocationCount(e.target.value)}
                    min="1"
                    max="100"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="itemCount">Number of Items</Label>
                  <Input
                    id="itemCount"
                    type="number"
                    value={itemCount}
                    onChange={e => setItemCount(e.target.value)}
                    min="1"
                    max="10000"
                  />
                </div>
              </div>
              <Button
                onClick={handleGenerateData}
                disabled={isGenerating}
              >
                {isGenerating ? 'Generating...' : 'Generate Test Inventory Data'}
              </Button>

              {/* Confirmation Dialog */}
              <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Confirm Data Generation</DialogTitle>
                    <DialogDescription>
                      Are you sure you want to generate test data? This will clear all existing inventory items and locations (user accounts will be preserved).
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>Cancel</Button>
                    <Button
                      onClick={() => {
                        setShowConfirmDialog(false);
                        doGenerateData();
                      }}
                      variant="destructive"
                    >
                      Confirm
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>

        <Card className="max-w-2xl mr-auto">
          <CardHeader>
            <CardTitle>Current Stats</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Total Locations</Label>
                <div className="text-2xl font-bold">{locations.length}</div>
              </div>
              <div>
                <Label>Total Items</Label>
                <div className="text-2xl font-bold">{items.length}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="max-w-2xl mr-auto">
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-muted p-4 rounded-lg space-y-2 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <div className="text-muted-foreground">No test results yet</div>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="font-mono text-sm">
                    {result}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
        <div className="flex gap-2">
        <BackButton />
      </div>
      </div>
    </>
  );
};

export default PerformanceTest;
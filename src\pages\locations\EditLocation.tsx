import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useInventory } from '@/context/InventoryContext';
import { useQuickLinks } from '@/context/QuickLinksContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { validateLocationName, validateLocationFields } from "@/utils/formUtils";
import { useFormValidation } from "@/hooks/useFormValidation";
import { Location } from "@/types";
import BackButton from '@/components/ui/BackButton';
import { Star } from 'lucide-react';

const EditLocation = () => {
  const { locationId } = useParams<{ locationId: string }>();
  const { locations, updateLocation, getLocationById } = useInventory();
  const { addLocationLink, removeLocationLink, isLinkSelected } = useQuickLinks();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [color, setColor] = useState('#888888');
  const [isFavorite, setIsFavorite] = useState(false);
  const navigate = useNavigate();

  const { error, validate } = useFormValidation();
  
  // Load location details on mount
  useEffect(() => {
    if (locationId) {
      const location = getLocationById(locationId);
      if (location) {
        setName(location.name);
        setDescription(location.description);
        setColor(location.color || '#888888');
        // Check if this location is already in quick links
        const linkId = `location-${locationId}`;
        setIsFavorite(isLinkSelected(linkId));
      } else {
        navigate('/locations');
      }
    }
  }, [locationId, getLocationById, navigate, isLinkSelected]);
  
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
    validate(() => validateLocationName(newName, locations, locationId));
  };

  const handleFavoriteToggle = (checked: boolean) => {
    if (!locationId) return;

    setIsFavorite(checked);

    if (checked) {
      addLocationLink(locationId);
    } else {
      const linkId = `location-${locationId}`;
      removeLocationLink(linkId);
    }
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!locationId) return;

    const updatedLocation: Partial<Location> = {
      name: name.trim(),
      description: description.trim(),
      color: color,
    };

    // Validate all fields before submission
    const nameValidation = validate(() => validateLocationName(name, locations, locationId));
    const fieldsValidation = validate(() => validateLocationFields(updatedLocation));

    if (!nameValidation || !fieldsValidation) return;

    try {
      updateLocation(locationId, updatedLocation);
      navigate(-1);
    } catch (err) {
      if (err instanceof Error) {
        validate(() => ({ isValid: false, error: err.message }));
      }
    }
  };
  
  if (!locationId) return null;
  
  return (
    <div className="space-y-6 animate-slide-in">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">Edit Location</h1>
            <p className="text-muted-foreground">
              Update the details of this storage location.
            </p>
          </div>
          <div className="flex gap-2">
            <BackButton />
          </div>
        </div>
        
        <Card className="max-w-2xl mr-auto">
          <CardHeader>
            <CardTitle>Location Information</CardTitle>
            <CardDescription>
              Update the details of this storage location.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-sm">
            <form onSubmit={handleSubmit} className="flex flex-col gap-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Location Name</Label>
                  <Input 
                    id="name"
                    value={name}
                    onChange={handleNameChange}
                    placeholder="Enter location name"
                    className={error ? 'border-destructive' : ''}
                    required
                  />
                  {error && (
                    <p className="text-sm text-destructive">{error}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter location description"
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">Location Color</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="color"
                      type="color"
                      value={color}
                      onChange={(e) => setColor(e.target.value)}
                      className="p-1 h-10 w-14 block border border-input rounded-md cursor-pointer"
                    />
                     <span className="text-sm text-muted-foreground">{color}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="favorite"
                    checked={isFavorite}
                    onCheckedChange={handleFavoriteToggle}
                  />
                  <div className="grid gap-1.5 leading-none">
                    <div className="flex items-center gap-2">
                      <Label
                        htmlFor="favorite"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        <Star className="h-4 w-4 inline mr-1" />
                        Add to Quick Links
                      </Label>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Add this location to your sidebar quick links for easy access
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end gap-4">
                <Button 
                  type="button"
                  variant="outline" 
                  onClick={() => navigate(-1)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={!!error || !name.trim()}>
                  Update Location
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
        <div className="flex gap-2">
          <BackButton />
        </div>
      </div>
  );
};

export default EditLocation;

#!/bin/bash

# Rebuild script for Imbentory application
echo "🔄 Rebuilding Imbentory application..."

# Clean up previous build
echo "🧹 Cleaning up previous build..."
rm -rf dist

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
  echo "📦 Installing dependencies..."
  npm install
fi

# Build the application
echo "🏗️ Building application..."
npm run build

# Copy service worker to dist directory
echo "📋 Copying service worker to dist directory..."
cp public/background.js dist/

echo "✅ Build completed successfully!"
echo "You can now run the application with 'npm run dev' or 'npm run preview'"

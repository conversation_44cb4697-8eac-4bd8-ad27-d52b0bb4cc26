import { format, parseISO, addMinutes } from 'date-fns';

export const formatWithTimezone = (dateStr: string | null, formatStr: string = 'PPP', includeTime: boolean = false): string => {
  if (!dateStr) return '';
  
  const date = parseISO(dateStr);
  
  // Get the local timezone offset in minutes
  const localOffset = date.getTimezoneOffset();
  
  // Get the desired timezone offset from settings
  const desiredOffset = parseInt(localStorage.getItem('timezone')?.replace('UTC', '') || '0');
  
  // Adjust date for local and desired timezone
  const adjustedDate = addMinutes(addMinutes(date, localOffset), desiredOffset * 60);
  return format(adjustedDate, includeTime ? `${formatStr} HH:mm` : formatStr);
};

/**
 * Returns the date in UTC ISO string format.
 */
export const getUTCDate = (date: Date = new Date()): string => {
  return date.toISOString();
};
import { useState } from 'react';
import { ValidationResult } from '@/utils/formUtils';

/**
 * Props for the useFormValidation hook
 * @interface UseFormValidationProps
 * @property {string} [initialError] - Initial error message
 */
interface UseFormValidationProps {
  initialError?: string;
}

/**
 * Custom hook for form validation state management
 * 
 * @param {UseFormValidationProps} props - Configuration options
 * @returns {Object} Form validation state and methods
 * @property {string} error - Current error message
 * @property {Function} setError - Function to manually set error message
 * @property {Function} validate - Function to run validation and update error state
 * @property {Function} clearError - Function to clear the current error
 * @property {boolean} hasError - Whether there is a current error
 */
export const useFormValidation = ({ initialError = '' }: UseFormValidationProps = {}) => {
  const [error, setError] = useState(initialError);

  /**
   * Validates form data using the provided validation function
   * 
   * @param {() => ValidationResult} validationFn - Function that performs validation and returns result
   * @returns {boolean} Whether validation passed
   */
  const validate = (validationFn: () => ValidationResult): boolean => {
    const result = validationFn();
    setError(result.error);
    return result.isValid;
  };

  /**
   * Clears the current error message
   */
  const clearError = () => setError('');

  return {
    error,
    setError,
    validate,
    clearError,
    hasError: !!error
  };
}; 
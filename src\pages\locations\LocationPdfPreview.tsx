import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { FileDown, ChevronDown, Type, Table2, MoreHorizontal, Sliders, TextQuote, Rows, Calendar, Clock as ClockIcon } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useInventory } from '@/context/InventoryContext';
import { Page, Text, View, Document, StyleSheet, PDFViewer, PDFDownloadLink } from '@react-pdf/renderer';
import { format } from 'date-fns';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import BackButton from '@/components/ui/BackButton';

// PDF style options
interface PdfStyleOptions {
  /**
   * Font size for the header
   */
  headerFontSize: number;
  /**
   * Font size for the content
   */
  contentFontSize: number;
  /**
   * Vertical padding for the table rows
   */
  verticalPadding: 'compact' | 'normal' | 'spacious';
  /**
   * Show or hide the expiry column
   */
  showExpiryColumn: boolean;
  /**
   * Optional subtitle for the PDF
   */
  subtitle?: string;
  /**
   * Show or hide the date and time
   */
  showDateTime: boolean;
}

// Generate PDF styles based on options
const getStyles = (options: PdfStyleOptions) => StyleSheet.create({
  page: {
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    marginBottom: 20,
  },
  locationName: {
    fontSize: options.headerFontSize,
    marginBottom: 5,
    fontWeight: 'bold',
  },
  locationDescription: {
    fontSize: 10,
    color: '#666',
    marginBottom: 12,
  },
  table: {
    width: '100%',
    fontSize: options.contentFontSize,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#666',
    borderBottomStyle: 'solid',
    minHeight: options.verticalPadding === 'compact' ? 20 :
              options.verticalPadding === 'spacious' ? 32 : 24,
    paddingVertical: options.verticalPadding === 'compact' ? 1 :
                    options.verticalPadding === 'spacious' ? 6 : 4,
  },
  tableHeader: {
    backgroundColor: '#f4f4f4',
    fontWeight: 'bold',
  },
  tableCell: {
    padding: 5,
  },
  number: {
    width: '5%',
    textAlign: 'center'
  },
  name: { width: '20%' },
  quantity: { width: '10%', textAlign: 'center' },
  unit: { width: '10%', textAlign: 'center' },
  expiry: { width: '15%', textAlign: 'center' },
  description: { width: '40%' },
  pageNumber: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    fontSize: 10,
    color: '#666',
  },
  dateTime: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    fontSize: 10,
    color: '#666',
  },
  subtitle: {
    position: 'absolute',
    top: 30,
    right: 30,
    fontSize: 10,
    color: '#666',
  },
});

interface Location {
  id: string;
  name: string;
  description?: string;
}

interface InventoryItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  expiryDate?: string;
  description?: string;
}

interface LocationPdfDocumentProps {
  location: Location;
  items: InventoryItem[];
  options: PdfStyleOptions;
}

const LocationPdfDocument = ({ location, items, options }: LocationPdfDocumentProps) => {
  const styles = getStyles(options);

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {options.subtitle && (
          <Text style={styles.subtitle}>{options.subtitle}</Text>
        )}

        <View style={styles.header}>
          <Text style={styles.locationName}>{location.name}</Text>
          {location.description && (
            <Text style={styles.locationDescription}>{location.description}</Text>
          )}
        </View>

        <View style={styles.table}>
          <View style={[styles.tableRow, styles.tableHeader]}>
            <Text style={[styles.tableCell, styles.number]}>#</Text>
            <Text style={[styles.tableCell, styles.name]}>Name</Text>
            <Text style={[styles.tableCell, styles.quantity]}>Quantity</Text>
            <Text style={[styles.tableCell, styles.unit]}>Unit</Text>
            {options.showExpiryColumn && (
              <Text style={[styles.tableCell, styles.expiry]}>Expiry Date</Text>
            )}
            <Text style={[styles.tableCell, styles.description]}>Description</Text>
          </View>

          {items.map((item: InventoryItem, index: number) => (
            <View key={item.id} style={styles.tableRow}>
              <Text style={[styles.tableCell, styles.number]}>{index + 1}</Text>
              <Text style={[styles.tableCell, styles.name]}>{item.name}</Text>
              <Text style={[styles.tableCell, styles.quantity]}>{item.quantity}</Text>
              <Text style={[styles.tableCell, styles.unit]}>{item.unit}</Text>
              {options.showExpiryColumn && (
                <Text style={[styles.tableCell, styles.expiry]}>
                  {item.expiryDate ? format(new Date(item.expiryDate), 'MMM d, yyyy') : '—'}
                </Text>
              )}
              <Text style={[styles.tableCell, styles.description]}>{item.description || '—'}</Text>
            </View>
          ))}
        </View>

        {options.showDateTime && (
          <Text style={styles.dateTime}>
            {format(new Date(), 'PPpp')}
          </Text>
        )}

        <Text
          style={styles.pageNumber}
          render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`}
          fixed
        />
      </Page>
    </Document>
  );
};

const LocationPdfPreview = () => {
  const { locationId } = useParams<{ locationId: string }>();
  const navigate = useNavigate();
  const { getLocationById, getItemsInLocation } = useInventory();

  const location = getLocationById(locationId);
  const items = getItemsInLocation(locationId);

  const [isStyleOpen, setIsStyleOpen] = useState(false);
  const [headerFontSize, setHeaderFontSize] = useState(18);
  const [contentFontSize, setContentFontSize] = useState(10);
  const [verticalPadding, setVerticalPadding] = useState<'compact' | 'normal' | 'spacious'>('normal');
  const [showExpiryColumn, setShowExpiryColumn] = useState(true);
  const [subtitle, setSubtitle] = useState('');
  const [showDateTime, setShowDateTime] = useState(true);

  if (!location) {
    navigate('/locations');
    return null;
  }

  const pdfOptions = {
    headerFontSize,
    contentFontSize,
    verticalPadding,
    showExpiryColumn,
    subtitle,
    showDateTime
  };

  return (
    <>
      <div className="space-y-6 animate-slide-in">
        <div>
          <h1 className="text-3xl font-bold tracking-tight mb-4">Location PDF Preview</h1>
          <div className="flex justify-between items-center mb-4">
            <div>
              <p className="text-muted-foreground">
                Generate a printable inventory list for <strong>{location.name}</strong>
              </p>
            </div>

            <PDFDownloadLink
              document={<LocationPdfDocument location={location} items={items} options={pdfOptions} />}
              fileName={`${location.name.replace(/\s+/g, '_')}_inventory.pdf`}
              className="no-underline"
            >
              {({ loading }) => (
                <Button disabled={loading} className="gap-2">
                  <FileDown className="h-4 w-4" />
                  {loading ? 'Preparing PDF...' : 'Download PDF'}
                </Button>
              )}
            </PDFDownloadLink>
          </div>
        </div>

        <Card className="max-w-2xl mr-auto shadow-md border-slate-200 dark:border-slate-800 pt-0">
          <Collapsible open={isStyleOpen} onOpenChange={setIsStyleOpen}>
            <CardHeader className="cursor-pointer bg-slate-50 dark:bg-slate-900 rounded-t-lg p-3 mt-0" onClick={() => setIsStyleOpen(!isStyleOpen)}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                    <Sliders className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle>PDF Style Options</CardTitle>
                    <CardDescription>Customize the appearance of your PDF export</CardDescription>
                  </div>
                </div>
                <ChevronDown className={`h-5 w-5 transition-transform text-muted-foreground ${isStyleOpen ? 'rotate-180' : ''}`} />
              </div>
            </CardHeader>
            <CollapsibleContent>
              <CardContent className="pt-6">
                <div className="grid gap-8">
                  {/* Text Options */}
                  <div className="space-y-4 bg-slate-50/50 dark:bg-slate-900/50 p-4 rounded-lg border border-slate-200 dark:border-slate-800">
                    <div className="flex items-center gap-2 pb-2 border-b border-slate-200 dark:border-slate-800">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30">
                        <Type className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                      </div>
                      <Label className="text-lg font-medium">Typography</Label>
                    </div>
                    <div className="grid gap-6 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="headerFont" className="text-muted-foreground flex items-center gap-1">
                          <TextQuote className="h-4 w-4" /> Header Size
                        </Label>
                        <div className="flex items-center gap-2">
                          <Input
                            id="headerFont"
                            type="number"
                            min="12"
                            max="32"
                            value={headerFontSize}
                            onChange={(e) => setHeaderFontSize(Number(e.target.value))}
                            className="w-24"
                          />
                          <span className="text-sm text-muted-foreground">px</span>
                          <div className="ml-2 flex-1 h-8 bg-slate-100 dark:bg-slate-800 rounded-md flex items-center px-3">
                            <div style={{ fontSize: `${headerFontSize/2}px` }} className="font-bold truncate">Sample Text</div>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="contentFont" className="text-muted-foreground flex items-center gap-1">
                          <TextQuote className="h-4 w-4" /> Content Size
                        </Label>
                        <div className="flex items-center gap-2">
                          <Input
                            id="contentFont"
                            type="number"
                            min="8"
                            max="24"
                            value={contentFontSize}
                            onChange={(e) => setContentFontSize(Number(e.target.value))}
                            className="w-24"
                          />
                          <span className="text-sm text-muted-foreground">px</span>
                          <div className="ml-2 flex-1 h-8 bg-slate-100 dark:bg-slate-800 rounded-md flex items-center px-3">
                            <div style={{ fontSize: `${contentFontSize/2}px` }} className="truncate">Sample Text</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Table Options */}
                  <div className="space-y-4 bg-slate-50/50 dark:bg-slate-900/50 p-4 rounded-lg border border-slate-200 dark:border-slate-800">
                    <div className="flex items-center gap-2 pb-2 border-b border-slate-200 dark:border-slate-800">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30">
                        <Table2 className="h-4 w-4 text-green-500 dark:text-green-400" />
                      </div>
                      <Label className="text-lg font-medium">Table Layout</Label>
                    </div>
                    <div className="grid gap-6 md:grid-cols-2">
                      <div className="space-y-3">
                        <Label className="text-muted-foreground flex items-center gap-1">
                          <Rows className="h-4 w-4" /> Row Spacing
                        </Label>
                        <RadioGroup
                          value={verticalPadding}
                          onValueChange={(value: 'compact' | 'normal' | 'spacious') => setVerticalPadding(value)}
                          className="grid grid-cols-3 gap-2"
                        >
                          <div className="flex flex-col items-center gap-1 border border-slate-200 dark:border-slate-800 rounded-md p-2 hover:bg-slate-100 dark:hover:bg-slate-800 cursor-pointer transition-colors">
                            <div className={`w-full h-10 bg-slate-100 dark:bg-slate-800 rounded flex flex-col justify-between p-1 ${verticalPadding === 'compact' ? 'ring-2 ring-primary' : ''}`}>
                              <div className="h-1.5 w-full bg-slate-300 dark:bg-slate-700 rounded"></div>
                              <div className="h-1.5 w-full bg-slate-300 dark:bg-slate-700 rounded"></div>
                              <div className="h-1.5 w-full bg-slate-300 dark:bg-slate-700 rounded"></div>
                            </div>
                            <RadioGroupItem value="compact" id="compact" className="sr-only" />
                            <Label htmlFor="compact" className="text-xs cursor-pointer">Compact</Label>
                          </div>
                          <div className="flex flex-col items-center gap-1 border border-slate-200 dark:border-slate-800 rounded-md p-2 hover:bg-slate-100 dark:hover:bg-slate-800 cursor-pointer transition-colors">
                            <div className={`w-full h-10 bg-slate-100 dark:bg-slate-800 rounded flex flex-col justify-between p-1 ${verticalPadding === 'normal' ? 'ring-2 ring-primary' : ''}`}>
                              <div className="h-2 w-full bg-slate-300 dark:bg-slate-700 rounded"></div>
                              <div className="h-2 w-full bg-slate-300 dark:bg-slate-700 rounded"></div>
                            </div>
                            <RadioGroupItem value="normal" id="normal" className="sr-only" />
                            <Label htmlFor="normal" className="text-xs cursor-pointer">Normal</Label>
                          </div>
                          <div className="flex flex-col items-center gap-1 border border-slate-200 dark:border-slate-800 rounded-md p-2 hover:bg-slate-100 dark:hover:bg-slate-800 cursor-pointer transition-colors">
                            <div className={`w-full h-10 bg-slate-100 dark:bg-slate-800 rounded flex flex-col justify-around p-1 ${verticalPadding === 'spacious' ? 'ring-2 ring-primary' : ''}`}>
                              <div className="h-2.5 w-full bg-slate-300 dark:bg-slate-700 rounded"></div>
                              <div className="h-2.5 w-full bg-slate-300 dark:bg-slate-700 rounded"></div>
                            </div>
                            <RadioGroupItem value="spacious" id="spacious" className="sr-only" />
                            <Label htmlFor="spacious" className="text-xs cursor-pointer">Spacious</Label>
                          </div>
                        </RadioGroup>
                      </div>

                      <div className="space-y-3">
                        <Label className="text-muted-foreground flex items-center gap-1">
                          <Calendar className="h-4 w-4" /> Columns
                        </Label>
                        <div className="flex items-center h-full">
                          <div className={`flex-1 border border-slate-200 dark:border-slate-800 rounded-md p-3 hover:bg-slate-100 dark:hover:bg-slate-800 cursor-pointer transition-colors ${showExpiryColumn ? 'bg-slate-100 dark:bg-slate-800' : ''}`} onClick={() => setShowExpiryColumn(!showExpiryColumn)}>
                            <div className="flex items-center gap-2">
                              <Checkbox
                                id="showExpiry"
                                checked={showExpiryColumn}
                                onCheckedChange={(checked: boolean) => setShowExpiryColumn(checked)}
                                className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                              />
                              <Label htmlFor="showExpiry" className="cursor-pointer">Include Expiry Date Column</Label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Additional Options */}
                  <div className="space-y-4 bg-slate-50/50 dark:bg-slate-900/50 p-4 rounded-lg border border-slate-200 dark:border-slate-800">
                    <div className="flex items-center gap-2 pb-2 border-b border-slate-200 dark:border-slate-800">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/30">
                        <MoreHorizontal className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                      </div>
                      <Label className="text-lg font-medium">Additional Options</Label>
                    </div>
                    <div className="grid gap-6 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="subtitle" className="text-muted-foreground flex items-center gap-1">
                          <TextQuote className="h-4 w-4" /> Subtitle Text
                        </Label>
                        <Input
                          id="subtitle"
                          value={subtitle}
                          onChange={(e) => setSubtitle(e.target.value)}
                          placeholder="Enter subtitle text"
                          className="w-full"
                        />
                        <p className="text-xs text-muted-foreground">Appears in the top right corner of the PDF</p>
                      </div>

                      <div className="space-y-3">
                        <Label className="text-muted-foreground flex items-center gap-1">
                          <ClockIcon className="h-4 w-4" /> Footer Options
                        </Label>
                        <div className="flex items-center h-full">
                          <div className={`flex-1 border border-slate-200 dark:border-slate-800 rounded-md p-3 hover:bg-slate-100 dark:hover:bg-slate-800 cursor-pointer transition-colors ${showDateTime ? 'bg-slate-100 dark:bg-slate-800' : ''}`} onClick={() => setShowDateTime(!showDateTime)}>
                            <div className="flex items-center gap-2">
                              <Checkbox
                                id="showDateTime"
                                checked={showDateTime}
                                onCheckedChange={(checked: boolean) => setShowDateTime(checked)}
                                className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                              />
                              <Label htmlFor="showDateTime" className="cursor-pointer">Show Date and Time in Footer</Label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        <div className="h-[800px] w-full border rounded-lg overflow-hidden">
          <PDFViewer width="100%" height="100%">
            <LocationPdfDocument
              location={location}
              items={items}
              options={pdfOptions}
            />
          </PDFViewer>
        </div>
        <div className="flex gap-2">
          <BackButton />
        </div>
      </div>
    </>
  );
};

export default LocationPdfPreview;

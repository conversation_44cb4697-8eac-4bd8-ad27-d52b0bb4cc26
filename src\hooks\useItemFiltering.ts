import { useMemo } from 'react';
import { Item, Location } from '@/types';
import { parseISO, isBefore, addDays } from 'date-fns';

/**
 * Props for the useItemFiltering hook
 */
interface UseItemFilteringProps {
  items: Item[];
  locations: Location[]; // Need locations for filtering by name
  searchTerm: string;
  sortField: keyof Item | 'locationName'; // Allow sorting by location name
  sortDirection: 'asc' | 'desc';
  filterLocation: string; // 'all' or locationId
  expiryFilter: string; // 'none', 'expired', '30', '60', '90'
  filterLowStock: boolean;
  lowStockThreshold: number; // Pass threshold as prop
}

/**
 * Custom hook for filtering and sorting items based on AllItems page logic
 *
 * @param {UseItemFilteringProps} props - Configuration for filtering and sorting
 * @returns {Item[]} Filtered and sorted items
 */
export function useItemFiltering({
  items,
  locations,
  searchTerm,
  sortField,
  sortDirection,
  filterLocation,
  expiryFilter,
  filterLowStock,
  lowStockThreshold,
}: UseItemFilteringProps): Item[] {

  const locationMap = useMemo(() => {
    const map = new Map<string, Location>();
    locations.forEach(loc => map.set(loc.id, loc));
    return map;
  }, [locations]);

  const processedItems = useMemo(() => {
    // --- Filtering ---
    const filtered = items.filter(item => {
      let include = true;

      // Location filter
      if (filterLocation !== 'all' && item.locationId !== filterLocation) {
        include = false;
      }

      // Expiry filter
      if (include && expiryFilter !== 'none') {
        // Get user's timezone setting
        const timezoneOffset = parseInt(localStorage.getItem('timezone')?.replace('UTC', '') || '0');

        // Get today's date at start of day in user's timezone
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Parse the expiry date (if it exists)
        const expiryDate = item.expiryDate ? parseISO(item.expiryDate) : null;

        if (!expiryDate) {
          // Items without expiry date are excluded from expiry filters
          include = false;
        } else if (expiryFilter === 'expired') {
          // For expired items: expiry date must be before today
          // Apply timezone adjustment if needed
          const adjustedExpiryDate = new Date(expiryDate);
          // Adjust for timezone if needed
          if (timezoneOffset !== 0) {
            adjustedExpiryDate.setHours(adjustedExpiryDate.getHours() + timezoneOffset);
          }
          include = isBefore(adjustedExpiryDate, today);
        } else {
          // For expiring items: expiry date must be in the future but within the threshold
          const days = parseInt(expiryFilter);
          const futureDate = addDays(today, days);
          // Apply timezone adjustment if needed
          const adjustedExpiryDate = new Date(expiryDate);
          // Adjust for timezone if needed
          if (timezoneOffset !== 0) {
            adjustedExpiryDate.setHours(adjustedExpiryDate.getHours() + timezoneOffset);
          }

          // Item is included if:
          // 1. It's not expired (on or after today)
          // 2. It expires within the threshold period
          include = !isBefore(adjustedExpiryDate, today) && isBefore(adjustedExpiryDate, futureDate);
        }
      }

      // Search filter
      if (include && searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const location = locationMap.get(item.locationId);
        const nameMatch = item.name.toLowerCase().includes(searchLower);
        const descMatch = item.description?.toLowerCase().includes(searchLower);
        const locMatch = location?.name.toLowerCase().includes(searchLower);
        const unitMatch = item.unit.toLowerCase().includes(searchLower);
        if (!(nameMatch || descMatch || locMatch || unitMatch)) {
          include = false;
        }
      }

      // Low stock filter
      if (include && filterLowStock) {
          if (!item.watchStock || item.quantity > lowStockThreshold) {
              include = false;
          }
      }

      return include;
    });

    // --- Sorting ---
    filtered.sort((a, b) => {
      let aValue: string | number | null | undefined;
      let bValue: string | number | null | undefined;

      if (sortField === 'locationName') {
        aValue = locationMap.get(a.locationId)?.name || '';
        bValue = locationMap.get(b.locationId)?.name || '';
      } else {
        aValue = a[sortField as keyof Item];
        bValue = b[sortField as keyof Item];
      }

      // Handle specific types
      if (sortField === 'expiryDate') {
        aValue = a.expiryDate ? new Date(a.expiryDate).getTime() : (sortDirection === 'asc' ? Infinity : -Infinity); // Handle nulls based on direction
        bValue = b.expiryDate ? new Date(b.expiryDate).getTime() : (sortDirection === 'asc' ? Infinity : -Infinity);
      } else if (sortField === 'createdAt' || sortField === 'updatedAt') {
         aValue = aValue ? new Date(aValue as string).getTime() : 0;
         bValue = bValue ? new Date(bValue as string).getTime() : 0;
      }

      // General comparison logic
      if (aValue === bValue) return 0;
      if (aValue === null || aValue === undefined) return 1; // Nulls/undefined last
      if (bValue === null || bValue === undefined) return -1; // Nulls/undefined last

      let comparison = 0;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        // Fallback comparison
        comparison = String(aValue).localeCompare(String(bValue));
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });

    return filtered;
  // locations is used in the locationMap creation, but doesn't need to be a dependency

  }, [
    items,
    locationMap, // Add map dependency
    filterLocation,
    expiryFilter,
    filterLowStock,
    searchTerm,
    sortField,
    sortDirection,
    lowStockThreshold,
  ]);

  return processedItems;
}
import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import * as authUtils from '@/utils/authUtils';
import { User } from '@/types';
import { toast } from 'sonner';
import { formatWithTimezone } from '@/utils/dateUtils';
import { PlusCircle, Trash2, Edit, Users, Mail, User as UserIcon, CheckCircle, XCircle, Clock, KeyRound, Shield, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { resizeAndCompressImage } from '@/utils/imageUtils';

// User management tab content. Only rendered for admin users. Provides admin controls for user CRUD, approval, and role management.
const UserManagementTabContent: React.FC = () => {
  const { user: adminUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [sortColumn, setSortColumn] = useState<string>('username');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [resetSortColumn, setResetSortColumn] = useState<string>('username');
  const [resetSortDirection, setResetSortDirection] = useState<'asc' | 'desc'>('asc');
  const [pendingSortColumn, setPendingSortColumn] = useState<string>('username');
  const [pendingSortDirection, setPendingSortDirection] = useState<'asc' | 'desc'>('asc');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRegisterDialogOpen, setIsRegisterDialogOpen] = useState(false);
  const [newUsername, setNewUsername] = useState('');
  const [newFullName, setNewFullName] = useState('');
  const [newEmail, setNewEmail] = useState('');
  const [newAvatarUrl, setNewAvatarUrl] = useState<string | undefined>(undefined);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [newRole, setNewRole] = useState<'admin' | 'user'>('user');
  const [registerError, setRegisterError] = useState<string | null>(null);
  const [isRegistering, setIsRegistering] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [userToEdit, setUserToEdit] = useState<User | null>(null);
  const [editUsername, setEditUsername] = useState('');
  const [editFullName, setEditFullName] = useState('');
  const [editEmail, setEditEmail] = useState('');
  const [editAvatarUrl, setEditAvatarUrl] = useState<string | undefined>(undefined);
  const [editAvatarPreview, setEditAvatarPreview] = useState<string | null>(null);
  const [editPassword, setEditPassword] = useState('');
  const [confirmEditPassword, setConfirmEditPassword] = useState('');
  const [editRole, setEditRole] = useState<'admin' | 'user'>('user');
  const [editError, setEditError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [userToApprove, setUserToApprove] = useState<User | null>(null);
  const [userToReject, setUserToReject] = useState<User | null>(null);
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [pendingUsers, setPendingUsers] = useState<User[]>([]);
  const [passwordResetUsers, setPasswordResetUsers] = useState<User[]>([]);

  // Function to fetch users
  const fetchUsers = useCallback(() => {
    if (adminUser) {
      try {
        setIsLoading(true);
        const fetchedUsers = authUtils.getUsersByAdmin(adminUser);

        // Separate users by status
        const pending = fetchedUsers.filter(user => user.status === 'pending');
        const passwordReset = fetchedUsers.filter(user => user.status === 'password_reset');
        const others = fetchedUsers.filter(user =>
          user.status !== 'pending' && user.status !== 'password_reset'
        );

        setPendingUsers(pending);
        setPasswordResetUsers(passwordReset);
        setUsers(others);
        setError(null);
      } catch (err) {
        console.error("Failed to fetch users:", err);
        setError("Failed to load user list.");
        setUsers([]);
        setPendingUsers([]);
      } finally {
        setIsLoading(false);
      }
    } else {
      setError("Admin context not available.");
      setIsLoading(false);
    }
  }, [adminUser]);

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Reset dialog state when closed
  useEffect(() => {
    if (!isRegisterDialogOpen) {
      setNewUsername('');
      setNewFullName('');
      setNewEmail('');
      setNewAvatarUrl(undefined);
      setAvatarPreview(null);
      setNewPassword('');
      setConfirmNewPassword('');
      setRegisterError(null);
      setIsRegistering(false);
    }
  }, [isRegisterDialogOpen]);

  // Reset edit dialog state when closed or user changes
  useEffect(() => {
    if (userToEdit) {
      setEditUsername(userToEdit.username);
      setEditFullName(userToEdit.fullName || '');
      setEditEmail(userToEdit.email || '');
      setEditAvatarUrl(userToEdit.avatarUrl);
      setEditAvatarPreview(userToEdit.avatarUrl || null);
      setEditPassword(''); // Clear password fields when opening
      setConfirmEditPassword('');
      setEditError(null);
      setIsEditing(false);
    } else {
      // Clear fields if dialog is closed without saving
      setEditUsername('');
      setEditFullName('');
      setEditEmail('');
      setEditAvatarUrl(undefined);
      setEditAvatarPreview(null);
      setEditPassword('');
      setConfirmEditPassword('');
      setEditError(null);
      setIsEditing(false);
    }
  }, [userToEdit]);

  // Handles avatar upload for a new user during registration.
  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Only accept image files
      if (!file.type.startsWith('image/')) {
        setRegisterError("Please select an image file");
        return;
      }

      // Maximum file size (e.g., 1MB)
      if (file.size > 1024 * 1024) {
        setRegisterError("Image file is too large (max 1MB)");
        return;
      }

      // Resize and compress
      const resizedImage = await resizeAndCompressImage(file, 100);
      setNewAvatarUrl(resizedImage);
      setAvatarPreview(resizedImage);
    } catch (error) {
      console.error("Error processing image:", error);
      setRegisterError("Failed to process image");
    }
  };

  // Handles avatar upload when editing an existing user.
  const handleEditAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Only accept image files
      if (!file.type.startsWith('image/')) {
        setEditError("Please select an image file");
        return;
      }

      // Maximum file size (e.g., 1MB)
      if (file.size > 1024 * 1024) {
        setEditError("Image file is too large (max 1MB)");
        return;
      }

      // Resize and compress
      const resizedImage = await resizeAndCompressImage(file, 100);
      setEditAvatarUrl(resizedImage);
      setEditAvatarPreview(resizedImage);
    } catch (error) {
      console.error("Error processing image:", error);
      setEditError("Failed to process image");
    }
  };

  const handleRegisterSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    if (!adminUser) return;

    setRegisterError(null);
    if (!newUsername.trim()) {
      setRegisterError("Username is required.");
      return;
    }
    if (!newEmail.trim()) {
      setRegisterError("Email is required.");
      return;
    }
    if (newPassword.length < 6) {
      setRegisterError("Password must be at least 6 characters long.");
      return;
    }
    if (newPassword !== confirmNewPassword) {
      setRegisterError("Passwords do not match.");
      return;
    }

    setIsRegistering(true);
    try {
      const success = await authUtils.registerUserByAdmin(adminUser, {
        username: newUsername.trim(),
        password: newPassword,
        fullName: newFullName,
        email: newEmail,
        avatarUrl: newAvatarUrl,
        role: newRole
      });

      if (success) {
        toast.success("User Registered", {
          description: `User "${newUsername.trim()}" created successfully.`
        });
        setIsRegisterDialogOpen(false);
        fetchUsers();
      } else {
        setRegisterError("Failed to register user. Username might already exist.");
      }
    } catch (err) {
      console.error("Registration error:", err);
      setRegisterError("An unexpected error occurred during registration.");
    } finally {
      setIsRegistering(false);
    }
  }, [adminUser, newUsername, newPassword, confirmNewPassword, newFullName, newEmail, newAvatarUrl, fetchUsers]);

  const handleDeleteClick = (user: User) => {
    setUserToDelete(user);
  };

  const confirmDeleteUser = useCallback(async () => {
    if (!adminUser || !userToDelete) return;

    setIsDeleting(true);
    try {
      const success = await authUtils.deleteUserByAdmin(adminUser, userToDelete.username);
      if (success) {
        toast.success("User Deleted", {
          description: `User "${userToDelete.username}" has been deleted.`
        });
        setUserToDelete(null);
        fetchUsers();
      } else {
        toast.error("Deletion Failed", {
          description: `Could not delete user "${userToDelete.username}".`
        });
      }
    } catch (err) {
      console.error("Deletion error:", err);
      toast.error("Error", {
        description: "An unexpected error occurred during deletion."
      });
    } finally {
      setIsDeleting(false);
    }
  }, [adminUser, userToDelete, fetchUsers]);

  const handleEditClick = (user: User) => {
    const isPasswordReset = user.status === 'password_reset';

    setUserToEdit(user);
    setEditUsername(user.username);
    setEditFullName(user.fullName || '');
    setEditEmail(user.email || '');
    setEditAvatarUrl(user.avatarUrl);
    setEditAvatarPreview(user.avatarUrl || null);
    setEditRole(user.role);
    setEditPassword('');
    setConfirmEditPassword('');
    setEditError(null);

    // If this is a password reset user, show a message in the edit dialog
    if (isPasswordReset) {
      setEditError("This user has requested a password reset. Please set a new password.");
    }
  };

  const handleApproveClick = (user: User) => {
    setUserToApprove(user);
  };

  const handleRejectClick = (user: User) => {
    setUserToReject(user);
  };

  const confirmApproveUser = useCallback(async () => {
    if (!adminUser || !userToApprove) return;

    setIsApproving(true);
    try {
      const success = await authUtils.approveUser(adminUser, userToApprove.id);
      if (success) {
        toast.success("User Approved", {
          description: `User "${userToApprove.username}" has been approved and can now log in.`
        });
        setUserToApprove(null);
        fetchUsers();
      } else {
        toast.error("Approval Failed", {
          description: `Could not approve user "${userToApprove.username}".`
        });
      }
    } catch (err) {
      console.error("Approval error:", err);
      toast.error("Error", {
        description: "An unexpected error occurred during approval."
      });
    } finally {
      setIsApproving(false);
    }
  }, [adminUser, userToApprove, fetchUsers]);

  const confirmRejectUser = useCallback(async () => {
    if (!adminUser || !userToReject) return;

    setIsRejecting(true);
    try {
      const success = await authUtils.rejectUser(adminUser, userToReject.id);
      if (success) {
        toast.success("User Rejected", {
          description: `User "${userToReject.username}" has been rejected.`
        });
        setUserToReject(null);
        fetchUsers();
      } else {
        toast.error("Rejection Failed", {
          description: `Could not reject user "${userToReject.username}".`
        });
      }
    } catch (err) {
      console.error("Rejection error:", err);
      toast.error("Error", {
        description: "An unexpected error occurred during rejection."
      });
    } finally {
      setIsRejecting(false);
    }
  }, [adminUser, userToReject, fetchUsers]);

  const handleEditSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    if (!adminUser || !userToEdit) return;

    setEditError(null);
    if (!editUsername.trim()) {
      setEditError("Username is required.");
      return;
    }
    if (!editEmail.trim()) {
      setEditError("Email is required.");
      return;
    }

    // Check if this is a password reset user
    const isPasswordReset = userToEdit.status === 'password_reset';

    // For password reset users, a new password is required
    if (isPasswordReset && !editPassword) {
      setEditError("New password is required for password reset.");
      return;
    }

    // Validate passwords only if a new password is entered
    if (editPassword && editPassword.length < 6) {
      setEditError("New password must be at least 6 characters long.");
      return;
    }
    if (editPassword && editPassword !== confirmEditPassword) {
      setEditError("Passwords do not match.");
      return;
    }

    setIsEditing(true);
    try {
      const updateData: {
        username: string;
        password?: string;
        fullName?: string;
        email?: string;
        avatarUrl?: string;
        role?: 'admin' | 'user';
      } = {
        username: editUsername.trim(),
        fullName: editFullName.trim(),
        email: editEmail.trim(),
        avatarUrl: editAvatarUrl,
        role: editRole
      };

      if (editPassword) {
        updateData.password = editPassword; // Only include password if entered
      }

      const success = await authUtils.updateUserByAdmin(
        adminUser,
        userToEdit.id,
        updateData
      );

      if (success) {
        toast.success("User Updated", {
          description: `User "${editUsername.trim()}" updated successfully.`
        });
        setUserToEdit(null); // Close dialog on success
        fetchUsers(); // Refresh list
      } else {
        setEditError("Failed to update user. Username might be taken.");
      }
    } catch (err) {
      console.error("Update error:", err);
      setEditError("An unexpected error occurred during update.");
    } finally {
      setIsEditing(false);
    }
  }, [adminUser, userToEdit, editUsername, editPassword, confirmEditPassword, editFullName, editEmail, editAvatarUrl, editRole, fetchUsers]);

  return (
    <div className="space-y-6">
      {/* User Table */}
      {/* Password Reset Requests */}
      {passwordResetUsers.length > 0 && (
        <Card className="w-full mb-6 border-blue-200 dark:border-blue-800 ">
          <CardHeader className="">
            <div className="flex items-center gap-2">
              <KeyRound className="h-5 w-5 text-blue-600" />
              <CardTitle>Password Reset Requests</CardTitle>
            </div>
            <CardDescription>
              These users have requested a password reset and are waiting for your action.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (resetSortColumn === 'username') {
                          setResetSortDirection(resetSortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setResetSortColumn('username');
                          setResetSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        User
                        {resetSortColumn === 'username' ? (
                          resetSortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="hidden md:table-cell cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (resetSortColumn === 'fullName') {
                          setResetSortDirection(resetSortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setResetSortColumn('fullName');
                          setResetSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Full Name
                        {resetSortColumn === 'fullName' ? (
                          resetSortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="hidden md:table-cell cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (resetSortColumn === 'email') {
                          setResetSortDirection(resetSortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setResetSortColumn('email');
                          setResetSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Email
                        {resetSortColumn === 'email' ? (
                          resetSortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="hidden lg:table-cell cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (resetSortColumn === 'updatedAt') {
                          setResetSortDirection(resetSortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setResetSortColumn('updatedAt');
                          setResetSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Requested
                        {resetSortColumn === 'updatedAt' ? (
                          resetSortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...passwordResetUsers]
                    .sort((a, b) => {
                      // Handle sorting based on column and direction
                      if (resetSortColumn === 'username') {
                        return resetSortDirection === 'asc'
                          ? a.username.localeCompare(b.username)
                          : b.username.localeCompare(a.username);
                      } else if (resetSortColumn === 'fullName') {
                        const aName = a.fullName || '';
                        const bName = b.fullName || '';
                        return resetSortDirection === 'asc'
                          ? aName.localeCompare(bName)
                          : bName.localeCompare(aName);
                      } else if (resetSortColumn === 'email') {
                        const aEmail = a.email || '';
                        const bEmail = b.email || '';
                        return resetSortDirection === 'asc'
                          ? aEmail.localeCompare(bEmail)
                          : bEmail.localeCompare(aEmail);
                      } else if (resetSortColumn === 'updatedAt') {
                        return resetSortDirection === 'asc'
                          ? new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
                          : new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
                      }
                      return 0;
                    })
                    .map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-md overflow-hidden bg-muted flex items-center justify-center">
                            {user.avatarUrl ? (
                              <img
                                src={user.avatarUrl}
                                alt={user.username}
                                className="h-full w-full object-cover"
                              />
                            ) : (
                              <Users className="h-4 w-4 text-muted-foreground" />
                            )}
                          </div>
                          <span>{user.username}</span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {user.fullName || '-'}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {user.email || '-'}
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">{formatWithTimezone(user.updatedAt, 'PPp')}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditClick(user)}
                            disabled={isApproving || isRejecting}
                          >
                            <KeyRound className="h-4 w-4 mr-2" />
                            <span>Reset Password</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pending Users Card */}
      {pendingUsers.length > 0 && (
        <Card className="w-full mb-6 border-amber-200 dark:border-amber-800">
          <CardHeader className="">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-amber-600" />
              <CardTitle>Pending Approval</CardTitle>
            </div>
            <CardDescription>
              These users have registered and are waiting for your approval.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (pendingSortColumn === 'username') {
                          setPendingSortDirection(pendingSortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setPendingSortColumn('username');
                          setPendingSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        User
                        {pendingSortColumn === 'username' ? (
                          pendingSortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="hidden md:table-cell cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (pendingSortColumn === 'fullName') {
                          setPendingSortDirection(pendingSortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setPendingSortColumn('fullName');
                          setPendingSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Full Name
                        {pendingSortColumn === 'fullName' ? (
                          pendingSortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="hidden md:table-cell cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (pendingSortColumn === 'email') {
                          setPendingSortDirection(pendingSortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setPendingSortColumn('email');
                          setPendingSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Email
                        {pendingSortColumn === 'email' ? (
                          pendingSortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="hidden lg:table-cell cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (pendingSortColumn === 'createdAt') {
                          setPendingSortDirection(pendingSortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setPendingSortColumn('createdAt');
                          setPendingSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Registered
                        {pendingSortColumn === 'createdAt' ? (
                          pendingSortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...pendingUsers]
                    .sort((a, b) => {
                      // Handle sorting based on column and direction
                      if (pendingSortColumn === 'username') {
                        return pendingSortDirection === 'asc'
                          ? a.username.localeCompare(b.username)
                          : b.username.localeCompare(a.username);
                      } else if (pendingSortColumn === 'fullName') {
                        const aName = a.fullName || '';
                        const bName = b.fullName || '';
                        return pendingSortDirection === 'asc'
                          ? aName.localeCompare(bName)
                          : bName.localeCompare(aName);
                      } else if (pendingSortColumn === 'email') {
                        const aEmail = a.email || '';
                        const bEmail = b.email || '';
                        return pendingSortDirection === 'asc'
                          ? aEmail.localeCompare(bEmail)
                          : bEmail.localeCompare(aEmail);
                      } else if (pendingSortColumn === 'createdAt') {
                        return pendingSortDirection === 'asc'
                          ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
                          : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                      }
                      return 0;
                    })
                    .map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-md overflow-hidden bg-muted flex items-center justify-center">
                            {user.avatarUrl ? (
                              <img
                                src={user.avatarUrl}
                                alt={user.username}
                                className="h-full w-full object-cover"
                              />
                            ) : (
                              <Users className="h-4 w-4 text-muted-foreground" />
                            )}
                          </div>
                          <span>{user.username}</span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {user.fullName || '-'}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {user.email || '-'}
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">{formatWithTimezone(user.createdAt, 'PPp')}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-green-600 border-green-600 hover:bg-green-50 dark:hover:bg-green-950"
                            onClick={() => handleApproveClick(user)}
                            disabled={isApproving || isRejecting}
                          >
                            <CheckCircle className="h-4 w-4" />
                            <span className="sr-only md:not-sr-only md:ml-2">Approve</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 border-red-600 hover:bg-red-50 dark:hover:bg-red-950"
                            onClick={() => handleRejectClick(user)}
                            disabled={isApproving || isRejecting}
                          >
                            <XCircle className="h-4 w-4" />
                            <span className="sr-only md:not-sr-only md:ml-2">Reject</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Users Card */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle>User Accounts</CardTitle>
          <CardDescription>
            Manage all user accounts and access permissions.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-6">
              <div className="animate-pulse text-muted-foreground">Loading users...</div>
            </div>
          ) : error ? (
            <div className="bg-destructive/10 text-destructive rounded-md p-4 my-2">
              {error}
            </div>
          ) : (
            <div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (sortColumn === 'username') {
                          setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setSortColumn('username');
                          setSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        User
                        {sortColumn === 'username' ? (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="hidden md:table-cell cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (sortColumn === 'fullName') {
                          setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setSortColumn('fullName');
                          setSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Full Name
                        {sortColumn === 'fullName' ? (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="hidden md:table-cell cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (sortColumn === 'email') {
                          setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setSortColumn('email');
                          setSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Email
                        {sortColumn === 'email' ? (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (sortColumn === 'role') {
                          setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setSortColumn('role');
                          setSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Role
                        {sortColumn === 'role' ? (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="hidden lg:table-cell cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        if (sortColumn === 'createdAt') {
                          setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
                        } else {
                          setSortColumn('createdAt');
                          setSortDirection('asc');
                        }
                      }}
                    >
                      <div className="flex items-center gap-1">
                        Created
                        {sortColumn === 'createdAt' ? (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                        ) : <ArrowUpDown className="h-3 w-3 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center text-muted-foreground py-6">
                        No users found.
                      </TableCell>
                    </TableRow>
                  ) : (
                    [...users]
                      .sort((a, b) => {
                        // Handle sorting based on column and direction
                        if (sortColumn === 'username') {
                          return sortDirection === 'asc'
                            ? a.username.localeCompare(b.username)
                            : b.username.localeCompare(a.username);
                        } else if (sortColumn === 'fullName') {
                          const aName = a.fullName || '';
                          const bName = b.fullName || '';
                          return sortDirection === 'asc'
                            ? aName.localeCompare(bName)
                            : bName.localeCompare(aName);
                        } else if (sortColumn === 'email') {
                          const aEmail = a.email || '';
                          const bEmail = b.email || '';
                          return sortDirection === 'asc'
                            ? aEmail.localeCompare(bEmail)
                            : bEmail.localeCompare(aEmail);
                        } else if (sortColumn === 'role') {
                          return sortDirection === 'asc'
                            ? a.role.localeCompare(b.role)
                            : b.role.localeCompare(a.role);
                        } else if (sortColumn === 'createdAt') {
                          return sortDirection === 'asc'
                            ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
                            : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                        }
                        return 0;
                      })
                      .map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="h-8 w-8 rounded-md overflow-hidden bg-muted flex items-center justify-center">
                              {user.avatarUrl ? (
                                <img
                                  src={user.avatarUrl}
                                  alt={user.username}
                                  className="h-full w-full object-cover"
                                />
                              ) : (
                                <Users className="h-4 w-4 text-muted-foreground" />
                              )}
                            </div>
                            <span>{user.username}</span>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {user.fullName || '-'}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {user.email || '-'}
                        </TableCell>
                        <TableCell>
                          <Badge variant={
                            user.role === 'admin'
                              ? "destructive"
                              : user.role === 'user'
                                ? "outline"
                                : "secondary"
                          }>
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">{formatWithTimezone(user.createdAt, 'PPp')}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            {(
                              (user.username === "admin" && adminUser?.username === "admin") ||
                              (user.username !== "admin")
                            ) && (
                                <>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleEditClick(user)}
                                    disabled={isDeleting}
                                  >
                                    <Edit className="h-4 w-4" />
                                    <span className="sr-only">Edit</span>
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDeleteClick(user)}
                                    disabled={isDeleting}
                                    className={user.id === adminUser?.id ? "opacity-50 cursor-not-allowed" : ""}
                                  >
                                    <Trash2 className="h-4 w-4 text-destructive" />
                                    <span className="sr-only">Delete</span>
                                  </Button>
                                </>
                              )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}

          <div className="flex justify-between items-center mt-4">
            <Dialog open={isRegisterDialogOpen} onOpenChange={setIsRegisterDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <PlusCircle className="h-4 w-4" />
                  Create User
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[525px]">
                <DialogHeader>
                  <DialogTitle>Register New User</DialogTitle>
                  <DialogDescription>
                    Create a new user account.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleRegisterSubmit}>
                  <div className="grid gap-4 py-4">
                    {/* Avatar upload */}
                    <div className="flex flex-col gap-2">
                      <Label htmlFor="avatar-upload">Profile Picture</Label>
                      <div className="flex items-center gap-4">
                        <div className="h-16 w-16 rounded-full overflow-hidden bg-muted flex items-center justify-center border">
                          {avatarPreview ? (
                            <img
                              src={avatarPreview}
                              alt="Avatar preview"
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <Users className="h-8 w-8 text-muted-foreground" />
                          )}
                        </div>
                        <Input
                          id="avatar-upload"
                          type="file"
                          accept="image/*"
                          onChange={handleAvatarUpload}
                          className="max-w-64"
                        />
                      </div>
                    </div>

                    {/* Username */}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="new-username-tab" className="text-right">
                        <UserIcon className="h-4 w-4 inline mr-1" />
                        Username
                      </Label>
                      <Input
                        id="new-username-tab"
                        value={newUsername}
                        onChange={(e) => setNewUsername(e.target.value)}
                        className={cn("col-span-3", registerError?.includes("Username") ? 'border-destructive' : '')}
                        required
                      />
                    </div>

                    {/* Full Name */}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="new-fullname" className="text-right">
                        <Users className="h-4 w-4 inline mr-1" />
                        Name
                      </Label>
                      <Input
                        id="new-fullname"
                        value={newFullName}
                        onChange={(e) => setNewFullName(e.target.value)}
                        className="col-span-3"
                        placeholder="Enter your name"
                      />
                    </div>

                    {/* Email */}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="new-email" className="text-right">
                        <Mail className="h-4 w-4 inline mr-1" />
                        Email
                      </Label>
                      <Input
                        id="new-email"
                        type="email"
                        value={newEmail}
                        onChange={(e) => setNewEmail(e.target.value)}
                        className="col-span-3"
                        placeholder="Enter your email"
                      />
                    </div>

                    {/* Role Selection */}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="new-role" className="text-right">
                        <Shield className="h-4 w-4 inline mr-1" />
                        Role
                      </Label>
                      <Select value={newRole} onValueChange={(value) => setNewRole(value as 'admin' | 'user')}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="user">User</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Password */}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="new-password-tab" className="text-right">Password</Label>
                      <Input
                        id="new-password-tab"
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        className={cn("col-span-3", registerError?.includes("Password") || registerError?.includes("match") ? 'border-destructive' : '')}
                        required
                      />
                    </div>

                    {/* Confirm Password */}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="confirm-new-password-tab" className="text-right">Confirm</Label>
                      <Input
                        id="confirm-new-password-tab"
                        type="password"
                        value={confirmNewPassword}
                        onChange={(e) => setConfirmNewPassword(e.target.value)}
                        className={cn("col-span-3", registerError?.includes("match") ? 'border-destructive' : '')}
                        required
                      />
                    </div>

                    {registerError && (
                      <div className="bg-destructive/10 text-destructive rounded-md p-2 text-sm">
                        {registerError}
                      </div>
                    )}
                  </div>
                  <DialogFooter>
                    <Button type="submit" disabled={isRegistering}>
                      {isRegistering ? 'Registering...' : 'Register User'}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>

          </div>
        </CardContent>
      </Card>

      {/* Delete confirmation dialog */}
      <AlertDialog open={!!userToDelete} onOpenChange={(open) => !open && setUserToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete user "{userToDelete?.username}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteUser}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete User'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit User Dialog */}
      <Dialog open={!!userToEdit} onOpenChange={(open) => !open && setUserToEdit(null)}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Modify user information for <span className="font-medium">{userToEdit?.username}</span>.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit}>
            <div className="grid gap-4 py-4">
              {/* Avatar upload */}
              <div className="flex flex-col gap-2">
                <Label htmlFor="edit-avatar-upload">Profile Picture</Label>
                <div className="flex items-center gap-4">
                  <div className="h-16 w-16 rounded-full overflow-hidden bg-muted flex items-center justify-center border">
                    {editAvatarPreview ? (
                      <img
                        src={editAvatarPreview}
                        alt="Avatar preview"
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <Users className="h-8 w-8 text-muted-foreground" />
                    )}
                  </div>
                  <Input
                    id="edit-avatar-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleEditAvatarUpload}
                    className="max-w-64"
                  />
                </div>
              </div>

              {/* Username */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-username" className="text-right">
                  <UserIcon className="h-4 w-4 inline mr-1" />
                  Username
                </Label>
                <Input
                  id="edit-username"
                  value={editUsername}
                  onChange={(e) => setEditUsername(e.target.value)}
                  className={cn("col-span-3", editError?.includes("Username") ? 'border-destructive' : '')}
                  required
                />
              </div>

              {/* Full Name */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-fullname" className="text-right">
                  <Users className="h-4 w-4 inline mr-1" />
                  Name
                </Label>
                <Input
                  id="edit-fullname"
                  value={editFullName}
                  onChange={(e) => setEditFullName(e.target.value)}
                  className="col-span-3"
                  placeholder="Enter your name"
                />
              </div>

              {/* Email */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-email" className="text-right">
                  <Mail className="h-4 w-4 inline mr-1" />
                  Email
                </Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={editEmail}
                  onChange={(e) => setEditEmail(e.target.value)}
                  className="col-span-3"
                  placeholder="Enter your email"
                />
              </div>

              {/* Role Selection */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-role" className="text-right">
                  <Shield className="h-4 w-4 inline mr-1" />
                  Role
                </Label>
                <Select value={editRole} onValueChange={(value) => setEditRole(value as 'admin' | 'user')}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Password */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-password" className="text-right">New Password</Label>
                <Input
                  id="edit-password"
                  type="password"
                  placeholder="(Optional)"
                  value={editPassword}
                  onChange={(e) => setEditPassword(e.target.value)}
                  className={cn("col-span-3", editError?.includes("password") || editError?.includes("match") ? 'border-destructive' : '')}
                />
              </div>

              {/* Confirm Password */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="confirm-edit-password" className="text-right">Confirm</Label>
                <Input
                  id="confirm-edit-password"
                  type="password"
                  placeholder="(Optional)"
                  value={confirmEditPassword}
                  onChange={(e) => setConfirmEditPassword(e.target.value)}
                  className={cn("col-span-3", editError?.includes("match") ? 'border-destructive' : '')}
                />
              </div>

              {editError && (
                <div className="bg-destructive/10 text-destructive rounded-md p-2 text-sm">
                  {editError}
                </div>
              )}
            </div>
            <DialogFooter>
              <Button type="submit" disabled={isEditing}>
                {isEditing ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Approve User Dialog */}
      <AlertDialog open={!!userToApprove} onOpenChange={(open) => !open && setUserToApprove(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will approve user "{userToApprove?.username}" and grant them access to the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmApproveUser}
              disabled={isApproving}
              className="bg-green-600 text-white hover:bg-green-700"
            >
              {isApproving ? 'Approving...' : 'Approve User'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reject User Dialog */}
      <AlertDialog open={!!userToReject} onOpenChange={(open) => !open && setUserToReject(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will reject user "{userToReject?.username}" and they will not be able to access the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRejectUser}
              disabled={isRejecting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isRejecting ? 'Rejecting...' : 'Reject User'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default UserManagementTabContent;

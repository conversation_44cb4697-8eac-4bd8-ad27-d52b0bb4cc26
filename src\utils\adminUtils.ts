import { loadUsers, saveUsers } from './storageUtils';
import { hashPassword } from './authUtils';
import { v4 as uuidv4 } from 'uuid';
import { User } from '../types';
import adminConfig from '../config/admin.config';

/**
 * Checks if an admin user exists and creates one if none exists.
 * Only creates an admin if no users exist at all (first launch scenario).
 * Safe to run in production.
 */
export const ensureAdminExists = async (): Promise<void> => {
  // Skip admin creation if disabled in config
  if (!adminConfig.createOnFirstLaunch) {
    return;
  }

  try {
    const users = loadUsers();
    const adminExists = users.some(u => u.role === 'admin');

    // Only create an admin if no users exist at all
    if (users.length === 0) {
      console.log('No users found. Creating initial admin account...');

      const passwordHash = await hashPassword(adminConfig.password);
      const now = new Date().toISOString();

      const adminUser: User = {
        id: uuidv4(),
        username: adminConfig.username,
        passwordHash,
        role: 'admin',
        status: 'active', // Admin is always active
        fullName: adminConfig.fullName,
        email: adminConfig.email,
        avatarUrl: undefined,
        createdAt: now,
        updatedAt: now,
      };

      const success = await saveUsers([adminUser]);

      if (success) {
        console.log('Initial admin account created successfully.');
        // You could show a first-time setup notification here
      } else {
        console.error('Failed to create initial admin account.');
      }
    } else if (!adminExists) {
      // This is a warning scenario - users exist but no admin
      console.warn('Users exist but no admin account found. Consider promoting a user to admin.');
    }
  } catch (error) {
    console.error('Error during admin account verification:', error);
  }
};

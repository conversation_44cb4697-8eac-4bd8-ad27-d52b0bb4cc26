import React, { useState } from 'react';
import type { Item, Location } from '@/types';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useInventory } from '@/context/InventoryContext';
import { format } from 'date-fns';
import { Download, Upload, Save } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import BackButton from '@/components/ui/BackButton';

const ImportExport = () => {
  const { items, locations, addItem, addLocation } = useInventory();
  const [filename, setFilename] = useState<string>(() => {
    const now = new Date();
    return `inventory_${format(now, 'yyyy-MM-dd_HH-mm-ss')}.json`;
  });
  const [importFile, setImportFile] = useState<File | null>(null);

  const handleExport = () => {
    try {
      const data = {
        items,
        locations,
        exportDate: new Date().toISOString(),
        version: '1.0'
      };

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();

      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: `Data exported to ${filename}`,
      });
    } catch (error) {
      console.error("Export failed:", error);
      toast({
        title: "Export Failed",
        description: "There was a problem exporting your data.",
        variant: "destructive"
      });
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0]);
    }
  };

  const handleImport = () => {
    if (!importFile) {
      toast({
        title: "No File Selected",
        description: "Please select a file to import.",
        variant: "destructive"
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        if (typeof e.target?.result !== 'string') {
          throw new Error('Invalid file format');
        }

        const data = JSON.parse(e.target.result);

        // Validate structure
        if (!data.items || !data.locations || !Array.isArray(data.items) || !Array.isArray(data.locations)) {
          throw new Error('Invalid data structure');
        }

        // Helper to validate Item structure
        function isValidItem(obj: unknown): obj is Item {
          if (typeof obj !== 'object' || obj === null) return false;
          const o = obj as Record<string, unknown>;
          return (
            typeof o.id === 'string' &&
            typeof o.name === 'string' &&
            typeof o.quantity === 'number' &&
            typeof o.unit === 'string' &&
            typeof o.locationId === 'string' &&
            typeof o.description === 'string' &&
            (typeof o.expiryDate === 'string' || o.expiryDate === null) &&
            typeof o.watchStock === 'boolean' &&
            typeof o.createdAt === 'string' &&
            typeof o.updatedAt === 'string'
          );
        }

        // Helper to validate Location structure
        function isValidLocation(obj: unknown): obj is Location {
          if (typeof obj !== 'object' || obj === null) return false;
          const o = obj as Record<string, unknown>;
          return (
            typeof o.id === 'string' &&
            typeof o.name === 'string' &&
            typeof o.description === 'string' &&
            typeof o.createdAt === 'string' &&
            typeof o.updatedAt === 'string'
          );
        }

        // Import items
        data.items.forEach((item: Record<string, unknown>) => {
          if (isValidItem(item)) {
            const { updatedAt, ...itemWithoutUpdatedAt } = item;
            addItem(itemWithoutUpdatedAt as Omit<Item, 'updatedAt'>, true);
          } else {
            console.warn('Skipped invalid item during import:', item);
          }
        });

        // Import locations
        data.locations.forEach((location: Record<string, unknown>) => {
          if (isValidLocation(location)) {
            const { updatedAt, ...locationWithoutUpdatedAt } = location;
            addLocation(locationWithoutUpdatedAt as Omit<Location, 'updatedAt'>, true);
          } else {
            console.warn('Skipped invalid location during import:', location);
          }
        });

        toast({
          title: "Import Successful",
          description: `Imported ${data.items.length} items and ${data.locations.length} locations.`,
        });

        setImportFile(null);
        const fileInput = document.getElementById('file-input') as HTMLInputElement;
        if (fileInput) {
          fileInput.value = '';
        }

      } catch (error) {
        console.error("Import failed:", error);
        toast({
          title: "Import Failed",
          description: "The file format is invalid or corrupted.",
          variant: "destructive"
        });
      }
    };

    reader.readAsText(importFile);
  };

  const handleResetAllData = () => {
    localStorage.removeItem('inventory-items');
    localStorage.removeItem('inventory-locations');
    window.location.reload();
  };

  return (
    <Layout>
      <div className="space-y-8 animate-slide-in">
        <div className="flex flex-col space-y-2">
          <BackButton />
          <h1 className="text-3xl font-bold tracking-tight">Import & Export</h1>
          <p className="text-muted-foreground">
            Backup your inventory data or restore from a previous backup.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Export Card */}
          <Card className="max-w-2xl mr-auto">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Download className="mr-2 h-5 w-5" />
                Export Data
              </CardTitle>
              <CardDescription>
                Export your entire inventory database as a JSON file
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-col space-y-2">
                  <label htmlFor="filename" className="text-sm font-medium">
                    Filename
                  </label>
                  <Input
                    id="filename"
                    value={filename}
                    onChange={(e) => setFilename(e.target.value)}
                    placeholder="inventory_export.json"
                  />
                </div>
                <Button onClick={handleExport} className="w-full">
                  <Download className="mr-2 h-4 w-4" /> Export Database
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Import Card */}
          <Card className="max-w-2xl mr-auto">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="mr-2 h-5 w-5" />
                Import Data
              </CardTitle>
              <CardDescription>
                Import inventory data from a previously exported file
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-col space-y-2">
                  <label htmlFor="file-input" className="text-sm font-medium">
                    Select File
                  </label>
                  <Input
                    id="file-input"
                    type="file"
                    accept=".json"
                    onChange={handleFileChange}
                  />
                </div>
                <Button onClick={handleImport} className="w-full" disabled={!importFile}>
                  <Save className="mr-2 h-4 w-4" /> Import Database
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-destructive">Reset Data</h2>

          <div className="bg-background/60 backdrop-blur-sm rounded-lg border border-destructive/20 shadow-soft p-6">
            <h3 className="text-lg font-semibold mb-2">Danger Zone</h3>
            <p className="text-muted-foreground mb-4">
              Reset all inventory data. This action cannot be undone. Make sure to export your data first if you want to keep a backup.
            </p>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  Reset All Data
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will permanently delete all your inventory items and locations.
                    This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleResetAllData}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Yes, Delete Everything
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ImportExport;

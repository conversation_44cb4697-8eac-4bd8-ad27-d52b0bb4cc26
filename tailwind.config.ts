import type { Config } from "tailwindcss";

export default {
	darkMode: "class",
	content: [
		"./index.html", // Include the main HTML file
		"./src/**/*.{js,ts,jsx,tsx}", // Include all relevant file types within src
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1200px'
			}
		},
		fontFamily: {
			sans: ['<PERSON>eist', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
			mono: ['ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
		},
		extend: {
			colors: {
				border: "hsl(var(--border))",
				input: "hsl(var(--input))",
				ring: "hsl(var(--ring))",
				background: "hsl(var(--background))",
				foreground: "hsl(var(--foreground))",
				primary: {
				  DEFAULT: "hsl(var(--primary))",
				  foreground: "hsl(var(--primary-foreground))",
				},
				secondary: {
				  DEFAULT: "hsl(var(--secondary))",
				  foreground: "hsl(var(--secondary-foreground))",
				},
				destructive: {
				  DEFAULT: "hsl(var(--destructive) / <alpha-value>)",
				  foreground: "hsl(var(--destructive-foreground) / <alpha-value>)",
				},
				muted: {
				  DEFAULT: "hsl(var(--muted))",
				  foreground: "hsl(var(--muted-foreground))",
				},
				accent: {
				  DEFAULT: "hsl(var(--accent))",
				  foreground: "hsl(var(--accent-foreground))",
				},
				popover: {
				  DEFAULT: "hsl(var(--popover))",
				  foreground: "hsl(var(--popover-foreground))",
				},
				card: {
				  DEFAULT: "hsl(var(--card))",
				  foreground: "hsl(var(--card-foreground))",
				},
				sidebar: {
				  DEFAULT: "hsl(var(--sidebar-background))",
				  foreground: "hsl(var(--sidebar-foreground))",
				  primary: "hsl(var(--sidebar-primary))",
				  "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
				  accent: "hsl(var(--sidebar-accent))",
				  "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
				  border: "hsl(var(--sidebar-border))",
				  ring: "hsl(var(--sidebar-ring))",
				},
			  },
			borderRadius: {
				xl: "calc(var(--radius) + 4px)",
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)",
			},
			keyframes: {
				"accordion-down": {
				  from: { height: "0" },
				  to: { height: "var(--radix-accordion-content-height)" },
				},
				"accordion-up": {
				  from: { height: "var(--radix-accordion-content-height)" },
				  to: { height: "0" },
				},
				"caret-blink": {
				  "0%,70%,100%": { opacity: "1" },
				  "20%,50%": { opacity: "0" },
				},
			},
			backdropFilter: {
				'none': 'none',
				'blur': 'blur(8px)'
			},
			boxShadow: {
				'soft': '0 2px 5px rgba(0, 0, 0, 0.05)',
				'medium': '0 4px 10px rgba(0, 0, 0, 0.08)',
				'glass': '0 8px 16px rgba(0, 0, 0, 0.06)'
			}
		}
	},
	plugins: [/* @ts-expect-error - Plugin import */
		// eslint-disable-next-line @typescript-eslint/no-require-imports
		require("tailwindcss-animate")],
} satisfies Config;

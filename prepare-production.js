// Production preparation script
import { execSync } from 'child_process';
import fs from 'fs';

console.log('🚀 Starting production preparation process...');

// Check if build directories exist, suggesting a cleanup might be needed
if (fs.existsSync('./dist')) {
  console.log('\n⚠️ Previous build files detected. For a clean build, consider running:');
  console.log('npm run cleanup');
  console.log('or');
  console.log('npm run prod:clean-build');

  // Ask if user wants to continue
  console.log('\nContinuing with existing build files in 3 seconds...');
  // Sleep for 3 seconds to give user time to cancel (cross-platform)
  const startTime = Date.now();
  while (Date.now() - startTime < 3000) {
    // Wait without blocking the event loop
  }
}

try {
  // Step 1: Run ESLint with auto-fix
  console.log('\n📝 Step 1: Running ESLint with auto-fix...');
  try {
    // First try to fix what we can
    execSync('npx eslint . --fix', { stdio: 'inherit' });
    console.log('✅ ESLint auto-fix completed successfully!');
  } catch (error) {
    // If there are still errors, check if they're just warnings
    console.log('⚠️ ESLint found some issues that could not be auto-fixed.');
    console.log('Checking if there are only warnings remaining...');

    try {
      // Run with --max-warnings=1000 to allow warnings but fail on errors
      execSync('npx eslint . --max-warnings=1000', { stdio: 'pipe' });
      console.log('✅ Only warnings remain, proceeding with build.');
    } catch (err) {
      console.error('❌ ESLint found errors that must be fixed before building:');
      console.error(err.message);
      process.exit(1);
    }
  }

  // Step 2: Build the frontend
  console.log('\n🏗️ Step 2: Building the frontend...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Frontend build completed successfully!');
  } catch (buildError) {
    console.error('❌ Frontend build failed:', buildError.message);
    process.exit(1);
  }

  // Step 3: Check if Tauri is properly set up and build if it is
  if (fs.existsSync('./src-tauri')) {
    // Check if the Cargo.toml file exists in the src-tauri directory
    if (fs.existsSync('./src-tauri/Cargo.toml')) {
      console.log('\n📦 Step 3: Building the Tauri app...');
      try {
        // Check if tauri CLI is installed
        try {
          execSync('npx tauri --version', { stdio: 'pipe' });
        } catch (tauriCliError) {
          console.log('\n⚠️ Tauri CLI not found, attempting to install it...');
          execSync('npm install --save-dev @tauri-apps/cli', { stdio: 'inherit' });
        }

        // Check if cargo is available
        try {
          // Check if we're on Windows
          const isWindows = process.platform === 'win32';

          // On Windows, we need to be more careful with the command execution
          if (isWindows) {
            try {
              execSync('cargo --version', { stdio: 'pipe' });
            } catch (e) {
              // Try to find cargo in the PATH
              try {
                const cargoPath = execSync('where cargo', { encoding: 'utf8' }).trim().split('\r\n')[0];
                execSync(`"${cargoPath}" --version`, { stdio: 'pipe' });
              } catch (e2) {
                throw new Error('Cargo is not available. Please install Rust and Cargo: https://www.rust-lang.org/tools/install');
              }
            }
          } else {
            // Unix-like systems
            execSync('cargo --version', { stdio: 'pipe' });
          }
        } catch (cargoError) {
          throw new Error('Cargo is not available. Please install Rust and Cargo: https://www.rust-lang.org/tools/install');
        }

        // Run the Tauri build
        execSync('npm run tauri:build', { stdio: 'inherit' });
        console.log('✅ Tauri build completed successfully!');
      } catch (tauriError) {
        console.error('❌ Tauri build failed:', tauriError.message);
        console.log('\n⚠️ Frontend build was successful, but Tauri build failed.');
        console.log('You can still use the web version of the application.');
        // Don't exit with error since the frontend build was successful
      }
    } else {
      console.log('\n⚠️ Tauri project is not properly initialized (missing Cargo.toml).');
      console.log('Run `npx tauri init` to set up the Tauri project, or use the web version of the application.');
    }
  } else {
    console.log('\n⚠️ Tauri directory not found, skipping Tauri build.');
    console.log('Only the web version of the application will be available.');
  }

  console.log('\n🎉 Production preparation completed!');
  if (fs.existsSync('PRODUCTION_CHECKLIST.md')) {
    console.log('📋 Check the PRODUCTION_CHECKLIST.md for next steps.');
  }
} catch (error) {
  console.error('\n❌ Error during production preparation:', error.message);
  process.exit(1);
}

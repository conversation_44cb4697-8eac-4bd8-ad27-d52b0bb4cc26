import { parseISO, isBefore, addDays, addMinutes } from 'date-fns';

/**
 * Handles unit selection changes, supporting predefined and custom units.
 */
export const handleUnitChange = (
  setUnit: (value: string) => void,
  setIsCustomUnit: (value: boolean) => void
) => (value: string) => {
  if (value === 'custom') {
    setIsCustomUnit(true);
    setUnit('');
  } else {
    setIsCustomUnit(false);
    setUnit(value);
  }
};

/**
 * Returns CSS class for expiry status styling.
 */
export const getExpiryStatusStyle = (expiryDate: string | null): string => {
  if (!expiryDate) return '';

  const date = parseISO(expiryDate);
  const today = new Date();
  const timezoneOffset = parseInt(localStorage.getItem('timezone')?.replace('UTC', '') || '0');
  const adjustedDate = addMinutes(date, timezoneOffset * 60);
  const threshold = parseInt(localStorage.getItem('expiring-threshold') || '30');

  if (isBefore(adjustedDate, today)) {
    return 'text-destructive font-medium';
  }

  if (isBefore(adjustedDate, addDays(today, threshold))) {
    return 'text-amber-500 font-medium';
  }

  return '';
};

/**
 * Returns CSS class for low stock status styling.
 */
export const getLowStockStatusStyle = (quantity: number, watchStock: boolean): string => {
  if (!watchStock) return '';

  const threshold = parseInt(localStorage.getItem('low-stock-threshold') || '3');

  if (quantity <= 0) {
    return 'text-destructive font-medium';
  }
  
  if (quantity <= threshold) {
    return 'text-amber-500 font-medium';
  }
  
  return '';
}; 
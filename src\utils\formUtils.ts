import { Item, Location } from "@/types";
import { isEmpty, isValidName, isValidQuantity } from "./validationUtils"; // Import helpers

export interface ValidationResult {
  isValid: boolean;
  error: string;
}
// Duplicate checkers

export const isLocationNameTaken = (locations: Location[], name: string, currentLocationId?: string): boolean => {
  const lowerCaseName = name.toLowerCase();
  return locations.some(location =>
    location.name.toLowerCase() === lowerCaseName && location.id !== currentLocationId
  );
};

export const isItemNameTakenInLocation = (
  items: Item[],
  name: string,
  locationId: string,
  currentItemId?: string
): boolean => {
  const lowerCaseName = name.toLowerCase();
  return items.some(item =>
    item.name.toLowerCase() === lowerCaseName &&
    item.locationId === locationId &&
    item.id !== currentItemId
  );
};

// Validation functions

/**
 * Validates an item name within a location
 */
export const validateItemName = (
  name: string,
  items: Item[],
  locationId: string,
  currentItemId?: string
): ValidationResult => {
  // Use generic validation first
  if (!isValidName(name)) {
    return {
      isValid: false,
      error: isEmpty(name) ? "Name is required" : "Name exceeds maximum length" // Provide specific error
    };
  }

  // Check for duplicates using the helper
  const nameExists = isItemNameTakenInLocation(items, name, locationId, currentItemId);

  if (nameExists) {
    return {
      isValid: false,
      error: "An item with this name already exists in this location"
    };
  }

  return {
    isValid: true,
    error: ""
  };
};

/**
 * Validates a location name
 */
export const validateLocationName = (
  name: string,
  locations: Location[],
  currentLocationId?: string
): ValidationResult => {
  // Use generic validation first
  if (!isValidName(name)) {
    return {
      isValid: false,
      error: isEmpty(name) ? "Name is required" : "Name exceeds maximum length" // Provide specific error
    };
  }

  // Check for duplicates using the helper
  const nameExists = isLocationNameTaken(locations, name, currentLocationId);

  if (nameExists) {
    return {
      isValid: false,
      error: "A location with this name already exists"
    };
  }

  return {
    isValid: true,
    error: ""
  };
};

/**
 * Validates required fields for an item
 */
export const validateItemFields = (
  item: Partial<Item>
): ValidationResult => {
  // Use generic validation for name
  if (!isValidName(item.name)) {
     return {
       isValid: false,
       error: isEmpty(item.name) ? "Name is required" : "Name exceeds maximum length"
     };
   }

  if (!item.locationId) {
    return {
      isValid: false,
      error: "Location is required"
    };
  }

  // Validate quantity
  if (!isValidQuantity(item.quantity)) {
    return {
      isValid: false,
      error: "Quantity must be a non-negative number"
    };
  }

  // Validate unit
  if (isEmpty(item.unit)) {
    return {
      isValid: false,
      error: "Unit is required"
    };
  }

  return {
    isValid: true,
    error: ""
  };
};

/**
 * Validates required fields for a location
 */
export const validateLocationFields = (
  location: Partial<Location>
): ValidationResult => {
  // Use generic validation for name
  if (!isValidName(location.name)) {
    return {
      isValid: false,
      error: isEmpty(location.name) ? "Name is required" : "Name exceeds maximum length"
    };
  }

  return {
    isValid: true,
    error: ""
  };
}; 
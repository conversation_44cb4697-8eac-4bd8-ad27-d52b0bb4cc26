import { useState } from 'react';
import { Undo } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import QuickLinksSettings from './QuickLinksSettings';

const PreferencesSettings = () => {
  const [expiringThreshold, setExpiringThreshold] = useState(() => {
    const saved = localStorage.getItem('expiring-threshold');
    return saved ? parseInt(saved) : 30;
  });
  const [lowStockThreshold, setLowStockThreshold] = useState(() => {
    const saved = localStorage.getItem('low-stock-threshold');
    return saved ? parseInt(saved) : 3;
  });
  const [tempExpiringThreshold, setTempExpiringThreshold] = useState(expiringThreshold);
  const [tempLowStockThreshold, setTempLowStockThreshold] = useState(lowStockThreshold);
  const [timezone, setTimezone] = useState(() => {
    return localStorage.getItem('timezone') || 'UTC+0';
  });

  // Resets the expiring soon threshold to the default value (30 days) and updates localStorage.
  const resetThreshold = () => {
    setExpiringThreshold(30);
    setTempExpiringThreshold(30);
    localStorage.setItem('expiring-threshold', '30');
    toast.success("Settings Reset", {
      description: "Expiring soon threshold reset to 30 days."
    });
  };

  // Saves user preferences for expiring threshold, low stock threshold, and timezone to localStorage.
  const handleSavePreferences = () => {
    if (!isNaN(tempExpiringThreshold) && tempExpiringThreshold > 0) {
      setExpiringThreshold(tempExpiringThreshold);
      localStorage.setItem('expiring-threshold', tempExpiringThreshold.toString());
    }

    if (!isNaN(tempLowStockThreshold) && tempLowStockThreshold > 0) {
      setLowStockThreshold(tempLowStockThreshold);
      localStorage.setItem('low-stock-threshold', tempLowStockThreshold.toString());
    }

    // Save timezone and force a re-render of components that display dates
    localStorage.setItem('timezone', timezone);

    toast.success("Settings Updated", {
      description: "Your preferences have been saved. Please refresh the page to see the timezone changes.",
      duration: 5000,
    });
  };

  return (
    <div className="space-y-6 animate-slide-in">
      {/* Quick Links Settings */}
      <QuickLinksSettings />

      {/* Expiring Items and Low Stock Alert - Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Expiring Items</CardTitle>
            <CardDescription>
              Configure when items should be considered as "expiring soon".
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row items-start sm:items-end gap-4 max-w-[235px]">
              <div className="flex-1 space-y-2">
                <Label htmlFor="threshold">Days until expiry</Label>
                <Input
                  id="threshold"
                  type="number"
                  min="1"
                  value={tempExpiringThreshold}
                  onChange={(e) => setTempExpiringThreshold(parseInt(e.target.value) || 0)}
                />
              </div>
              <Button
                variant="outline"
                onClick={resetThreshold}
                className="flex items-center gap-2 w-full sm:w-auto"
              >
                <Undo className="h-4 w-4" />
                Reset
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full">
          <CardHeader>
            <CardTitle>Low Stock Alert</CardTitle>
            <CardDescription>
              Set the quantity threshold for low stock alerts.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="lowStockThreshold">Low stock threshold</Label>
              <Input
                id="lowStockThreshold"
                type="number"
                min="1"
                value={tempLowStockThreshold}
                onChange={(e) => setTempLowStockThreshold(parseInt(e.target.value) || 0)}
                className='max-w-[130px]'
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="w-full">
        <CardHeader>
          <CardTitle>Timezone</CardTitle>
          <CardDescription>
            Select your preferred timezone for displaying dates.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row items-start sm:items-end gap-4">
            <div className="flex-1 space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Select
                value={timezone}
                onValueChange={(value) => {
                  if (value === 'custom') {
                    const customOffset = prompt('Enter UTC offset (e.g., +5.5, -4, +8):');
                    if (customOffset) {
                      const parsedOffset = parseFloat(customOffset);
                      if (!isNaN(parsedOffset) && parsedOffset >= -12 && parsedOffset <= 14) {
                        const formattedOffset = parsedOffset >= 0 ? `UTC+${parsedOffset}` : `UTC${parsedOffset}`;
                        setTimezone(formattedOffset);
                      } else {
                        toast.error("Invalid Timezone", {
                          description: "Please enter a valid UTC offset between -12 and +14"
                        });
                      }
                    }
                    return;
                  }
                  setTimezone(value);
                }}
              >
                <SelectTrigger id="timezone">
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent className="max-h-[300px]">
                  <SelectItem value="UTC+0">UTC+0 (GMT)</SelectItem>
                  <SelectItem value="UTC-12">UTC-12 (Baker Island)</SelectItem>
                  <SelectItem value="UTC-11">UTC-11 (American Samoa)</SelectItem>
                  <SelectItem value="UTC-10">UTC-10 (Hawaii)</SelectItem>
                  <SelectItem value="UTC-9">UTC-9 (Alaska)</SelectItem>
                  <SelectItem value="UTC-8">UTC-8 (PST - Los Angeles)</SelectItem>
                  <SelectItem value="UTC-7">UTC-7 (MST - Phoenix)</SelectItem>
                  <SelectItem value="UTC-6">UTC-6 (CST - Chicago)</SelectItem>
                  <SelectItem value="UTC-5">UTC-5 (EST - New York)</SelectItem>
                  <SelectItem value="UTC-4">UTC-4 (AST - Halifax)</SelectItem>
                  <SelectItem value="UTC-3">UTC-3 (São Paulo)</SelectItem>
                  <SelectItem value="UTC-2">UTC-2 (Fernando de Noronha)</SelectItem>
                  <SelectItem value="UTC-1">UTC-1 (Azores)</SelectItem>
                  <SelectItem value="UTC+1">UTC+1 (London, Dublin)</SelectItem>
                  <SelectItem value="UTC+2">UTC+2 (Berlin, Paris)</SelectItem>
                  <SelectItem value="UTC+3">UTC+3 (Moscow)</SelectItem>
                  <SelectItem value="UTC+4">UTC+4 (Dubai)</SelectItem>
                  <SelectItem value="UTC+5">UTC+5 (Karachi)</SelectItem>
                  <SelectItem value="UTC+5.5">UTC+5:30 (India)</SelectItem>
                  <SelectItem value="UTC+6">UTC+6 (Dhaka)</SelectItem>
                  <SelectItem value="UTC+7">UTC+7 (Bangkok)</SelectItem>
                  <SelectItem value="UTC+8">UTC+8 (Singapore, Beijing)</SelectItem>
                  <SelectItem value="UTC+9">UTC+9 (Tokyo, Seoul)</SelectItem>
                  <SelectItem value="UTC+9.5">UTC+9:30 (Adelaide)</SelectItem>
                  <SelectItem value="UTC+10">UTC+10 (Sydney)</SelectItem>
                  <SelectItem value="UTC+11">UTC+11 (Solomon Islands)</SelectItem>
                  <SelectItem value="UTC+12">UTC+12 (Auckland)</SelectItem>
                  <SelectItem value="UTC+13">UTC+13 (Samoa)</SelectItem>
                  <SelectItem value="UTC+14">UTC+14 (Kiritimati)</SelectItem>
                  <SelectItem value="custom">Custom Offset...</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex">
        <Button onClick={handleSavePreferences} className="w-full sm:w-auto">
          Save Preferences
        </Button>
      </div>
    </div>
  );
};

export default PreferencesSettings;
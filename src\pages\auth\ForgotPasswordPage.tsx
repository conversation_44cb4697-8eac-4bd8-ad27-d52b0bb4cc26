import React, { useState, useCallback } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { BookHeart, ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils'; // Utility for conditional class name merging
import { Link } from 'react-router-dom';

/**
 * ForgotPasswordPage Component
 *
 * Renders the password reset request form. Handles email input, validation,
 * submission of reset requests, displays feedback/errors, and shows next steps.
 */
const ForgotPasswordPage: React.FC = () => {
  const { requestPasswordReset } = useAuth();

  // --- State ---
  // Local state for form field, feedback, and submission status
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInvalid, setIsInvalid] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // --- Handlers ---
  // Handle email input change and clear error state if needed
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (isInvalid) {
      setIsInvalid(false);
      setError(null);
    }
  };

  /**
   * Handles password reset request submission:
   * - Validates email field
   * - Calls requestPasswordReset
   * - Shows feedback on success or error
   */
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // --- Validation ---
    setError(null);
    setIsInvalid(false);

    if (!email.trim()) {
      setError("Email is required");
      setIsInvalid(true);
      return;
    }

    setIsSubmitting(true);

    try {
      const success = await requestPasswordReset(email.trim());

      if (success) {
        setIsSubmitted(true);
      } else {
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error("Password reset request error:", error);
      setError("An unexpected error occurred. Please try again.");
      setIsInvalid(true);
      setIsSubmitting(false);
    }
  }, [email, requestPasswordReset]);

  // --- Rendering ---
  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-md space-y-8">
        {/* App Title and Description Section */}
        <div className="flex flex-col items-center text-center">
          <BookHeart className="h-12 w-12 text-primary" />
          <h1 className="mt-2 text-2xl font-bold">Imbentorys</h1>
          <p className="text-sm text-muted-foreground">Reset your password</p>
        </div>

        {/* Password Reset Form Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Forgot Password</CardTitle>
            <CardDescription>
              {isSubmitted
                ? "Password reset request submitted"
                : "Enter your email to request a password reset"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isSubmitted ? (
              <div className="space-y-4">
                <div className="bg-primary/10 text-primary rounded-md p-3 text-sm">
                  Your password reset request has been submitted for {email}.
                </div>
                <div className="bg-muted p-3 rounded-md text-sm">
                  <p className="font-medium mb-1">What happens next?</p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>An administrator will review your request</li>
                    <li>They will reset your password manually</li>
                    <li>You will be contacted with your new password</li>
                  </ol>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={handleEmailChange}
                    required
                    aria-invalid={isInvalid}
                    className={cn(isInvalid && "border-destructive focus-visible:ring-destructive")}
                  />
                </div>
                {/* Error message for invalid reset attempts */}
                {error && (
                  <div className="bg-destructive/10 text-destructive rounded-md p-3 text-sm">
                    {error}
                  </div>
                )}
                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? 'Submitting...' : 'Request Password Reset'}
                </Button>
              </form>
            )}
          </CardContent>
          <CardFooter className="flex justify-center">
            <Link to="/login" className="text-primary hover:underline flex items-center gap-1">
              <ArrowLeft className="h-4 w-4" />
              Back to Login
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;

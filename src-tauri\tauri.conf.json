{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "My Imbentory", "version": "1.0.0", "identifier": "com.myimbentory.app", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:3001", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "<PERSON><PERSON><PERSON><PERSON> - Inventory Manager - by RAC v1 04/2025", "width": 1500, "height": 900, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/app.ico"]}}
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

export type FabAction = {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
};

export interface FloatingActionButtonProps {
  actions: FabAction[];
  className?: string;
}

export function FloatingActionButton({ actions, className }: FloatingActionButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className={cn(
      `fixed right-4 bottom-20 flex flex-col-reverse items-end gap-2 md:hidden z-50 ${isOpen ? 'pb-2' : ''}`,
      className
    )}>
      {isOpen && actions.map((action, index) => (
        <Button
          key={index}
          size="sm"
          className="h-12 rounded-full shadow-lg bg-primary text-primary-foreground hover:bg-primary/90 px-6"
          onClick={() => {
            setIsOpen(false);
            action.onClick();
          }}
        >
          {action.icon}
          <span className="ml-2">{action.label}</span>
        </Button>
      ))}
      
      <Button
        size="icon"
        className={`h-14 w-14 rounded-full shadow-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-transform ${isOpen ? 'rotate-45' : ''}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <Plus className="h-6 w-6" />
        <span className="sr-only">Add Menu</span>
      </Button>
    </div>
  );
} 
import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import * as authUtils from '@/utils/authUtils';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { UserCircle, Mail, User } from 'lucide-react';
import { resizeAndCompressImage } from '@/utils/imageUtils';

const UserSettings = () => {
  const { user, logout, refreshUser } = useAuth();
  
  // Profile fields
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(undefined);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isUpdatingProfile, setIsUpdatingProfile] = useState(false);
  const [profileError, setProfileError] = useState<string | null>(null);
  
  // Password fields
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // Initialize form with user data
  useEffect(() => {
    if (user) {
      setFullName(user.fullName || '');
      setEmail(user.email || '');
      setAvatarUrl(user.avatarUrl);
      setAvatarPreview(user.avatarUrl || null);
    }
  }, [user]);

  // Handles avatar upload for user profile, including validation and resizing.
  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    try {
      // Only accept image files
      if (!file.type.startsWith('image/')) {
        setProfileError("Please select an image file");
        return;
      }
      
      // Maximum file size (e.g., 1MB)
      if (file.size > 1024 * 1024) {
        setProfileError("Image file is too large (max 1MB)");
        return;
      }
      
      // Resize and compress
      const resizedImage = await resizeAndCompressImage(file, 100);
      setAvatarUrl(resizedImage);
      setAvatarPreview(resizedImage);
    } catch (error) {
      console.error("Error processing image:", error);
      setProfileError("Failed to process image");
    }
  };

  // Handle profile update
  const handleProfileUpdate = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    if (!user) return;

    setProfileError(null);
    setIsUpdatingProfile(true);
    
    try {
      const success = await authUtils.updateOwnProfile(
        user.id,
        {
          fullName: fullName,
          email: email,
          avatarUrl: avatarUrl
        }
      );

      if (success) {
        refreshUser();
        toast.success("Profile Updated", {
          description: "Your profile has been updated successfully."
        });
      } else {
        setProfileError("Failed to update profile.");
      }
    } catch (error) {
      console.error("Profile update error:", error);
      setProfileError("An unexpected error occurred.");
    } finally {
      setIsUpdatingProfile(false);
    }
  }, [user, fullName, email, avatarUrl, refreshUser]);

  // Handle password change
  const handlePasswordChange = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    if (!user) return;

    setPasswordError(null);

    if (!currentPassword || !newPassword || !confirmPassword) {
        setPasswordError("All password fields are required.");
        return;
    }
    if (newPassword.length < 6) {
      setPasswordError("New password must be at least 6 characters long.");
      return;
    }
    if (newPassword === currentPassword) {
        setPasswordError("New password cannot be the same as the current password.");
        return;
    }
    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords do not match.");
      return;
    }

    setIsChangingPassword(true);
    try {
      const success = await authUtils.changeOwnPassword(user.id, currentPassword, newPassword);

      if (success) {
        toast.success("Password Changed", {
          description: "Your password has been updated successfully. Please log in again."
        });
        logout();
      } else {
        setPasswordError("Failed to change password. Please check your current password.");
      }
    } catch (err) {
      console.error("Password change error:", err);
      setPasswordError("An unexpected error occurred.");
    } finally {
      setIsChangingPassword(false);
    }
  }, [user, currentPassword, newPassword, confirmPassword, logout]);

  if (!user) {
    return <div>Loading user profile...</div>;
  }

  return (
    <div className="space-y-6 animate-slide-in">
      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
          <CardDescription>Update your profile details and how you appear to others</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleProfileUpdate} className="space-y-6">
            {/* Avatar upload */}
            <div className="flex flex-col gap-4 sm:flex-row items-center sm:items-center">
              <div className="h-20 w-20 rounded-xl overflow-hidden bg-muted flex items-center justify-center">
                {avatarPreview ? (
                  <img 
                    src={avatarPreview} 
                    alt="Avatar preview" 
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <UserCircle className="h-12 w-12 text-muted-foreground" />
                )}
              </div>
              <div className="space-y-2 flex-1">
                <Label htmlFor="avatar-upload">Profile Picture</Label>
                <Input
                  id="avatar-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                  className="w-full max-w-md"
                />
                <p className="text-xs text-muted-foreground ml-2">
                  Upload a square image for best results. Maximum size: 1MB.
                </p>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="username">
                  <UserCircle className="h-3.5 w-3.5 inline-block mr-1" />
                  Username (can't be changed)
                </Label>
                <Input
                  id="username"
                  value={user.username}
                  disabled
                  className="bg-muted"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fullname">
                  <User className="h-3.5 w-3.5 inline-block mr-1" />
                  Name
                </Label>
                <Input
                  id="fullname"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder="Your full name"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">
                <Mail className="h-3.5 w-3.5 inline-block mr-1" />
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>

            {profileError && (
              <div className="bg-destructive/10 text-destructive rounded-md p-2 text-sm">
                {profileError}
              </div>
            )}

            <Button type="submit" disabled={isUpdatingProfile}>
              {isUpdatingProfile ? 'Saving...' : 'Save Profile'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Change Password</CardTitle>
          <CardDescription>
            Enter your current password and a new password.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handlePasswordChange} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="current-password">
                Current Password
              </Label>
              <Input
                id="current-password"
                type="password"
                placeholder="Enter your current password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                required
                className={cn(passwordError?.includes("current password") ? 'border-destructive' : '')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-password">
                New Password
              </Label>
              <Input
                id="new-password"
                type="password"
                placeholder="Enter new password (min. 6 characters)"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                required
                className={cn(passwordError?.includes("New password") || passwordError?.includes("match") ? 'border-destructive' : '')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-password">
                Confirm New Password
              </Label>
              <Input
                id="confirm-password"
                type="password"
                placeholder="Confirm the new password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                className={cn(passwordError?.includes("match") ? 'border-destructive' : '')}
              />
            </div>

            {passwordError && (
              <div className="bg-destructive/10 text-destructive rounded-md p-2 text-sm">
                {passwordError}
              </div>
            )}

            <Button type="submit" disabled={isChangingPassword} className="w-full sm:w-auto">
              {isChangingPassword ? 'Changing Password...' : 'Change Password'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserSettings; 
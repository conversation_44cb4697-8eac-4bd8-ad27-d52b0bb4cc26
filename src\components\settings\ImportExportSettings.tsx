import React, { useState } from 'react';
import { v4 as uuidv4 } from 'uuid'; // Import uuid
import { FileDown, FileText, Database } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { useInventory } from '@/context/InventoryContext';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from '@/components/ui/label';
import { Item, Location } from "@/types";
import { validateItemFields, validateLocationFields } from "@/utils/formUtils";
import { QUICK_LINKS_STORAGE_KEY } from '@/constants/quickLinks';

// Defines an extended item interface for import, allowing locationName for flexible mapping.
interface ImportItem extends Partial<Item> {
  locationName?: string; // Some imports might have locationName instead of locationId
  category?: string; // Optional category field for CSV imports
  brand?: string; // Optional brand field for CSV imports
  price?: number; // Optional price field for CSV imports
  barcode?: string; // Optional barcode field for CSV imports
}

interface ImportData {
  items?: ImportItem[];
  locations?: Partial<Location>[];
  settings?: {
    expiringThreshold?: number;
    lowStockThreshold?: number;
    quickLinks?: string[];
    timezone?: string;
  };
}

interface ValidationError {
  name: string;
  error: string;
  type: 'item' | 'location';
}

interface ImportExportSettingsProps {
  setSelectedFile: (file: File | null) => void;
  selectedFile: File | null;
  handleImport: () => void; // Keep this prop if needed by parent, though local handleImport exists
}

const ImportExportSettings = ({
  setSelectedFile,
  selectedFile,
  // handleImport: parentHandleImport, // Can likely remove if local handleImport is sufficient
}: ImportExportSettingsProps) => {
  const { items, locations, addItem, addLocation } = useInventory(); // resetDatabase removed

  // State variables for managing import process, validation, and user prompts.
  const [importData, setImportData] = useState<ImportData | null>(null);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [duplicateAction, setDuplicateAction] = useState<'overwrite' | 'rename'>('overwrite');
  const [itemsNeedingLocation, setItemsNeedingLocation] = useState<ImportItem[]>([]);
  const [locationAssignments, setLocationAssignments] = useState<Record<string, string>>({});
  // showResetConfirmLocal state removed

  // Handles file selection for import, storing the selected file in state.
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const fileExtension = file.name.toLowerCase().split('.').pop();
      if (fileExtension !== 'json' && fileExtension !== 'csv') {
        toast.error("Invalid File Type", {
          description: "Please select a JSON or CSV file."
        });
        return;
      }
      setSelectedFile(file);
    }
  };

  // Helper function to parse CSV row (handles quoted fields)
  const parseCSVRow = (row: string): string[] => {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let j = 0; j < row.length; j++) {
      const char = row[j];
      const nextChar = row[j + 1];

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          // Escaped quote
          current += '"';
          j++; // Skip next quote
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        // End of field
        result.push(current);
        current = '';
      } else {
        current += char;
      }
    }

    // Add the last field
    result.push(current);
    return result;
  };

  // CSV parsing function
  const parseCSV = (csvText: string): ImportData => {
    if (!csvText || csvText.trim().length === 0) {
      throw new Error('CSV file is empty');
    }

    const lines = csvText.split('\n').map(line => line.trim()).filter(line => line);
    const items: ImportItem[] = [];
    const locations: Partial<Location>[] = [];
    const settings: ImportData['settings'] = {
      expiringThreshold: 30,
      lowStockThreshold: 3,
      quickLinks: [],
      timezone: 'UTC+0'
    };

    if (lines.length === 0) {
      throw new Error('No valid lines found in CSV file');
    }

    // Check if this is the new comprehensive format
    const isComprehensiveFormat = lines[0].includes('INVENTORY_EXPORT_METADATA');

    if (isComprehensiveFormat) {
      return parseComprehensiveCSV(lines, items, locations, settings);
    } else {
      // Fall back to legacy format parsing
      return parseLegacyCSV(lines, items, locations, settings);
    }
  };

  // Parse comprehensive CSV format with all data
  const parseComprehensiveCSV = (lines: string[], items: ImportItem[], locations: Partial<Location>[], settings: ImportData['settings']): ImportData => {
    let currentSection = '';
    let headerMapping: { [key: string]: number } = {};

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (!line) continue;

      const fields = parseCSVRow(line);

      // Check for section headers
      if (fields.length === 1) {
        const sectionName = fields[0].toUpperCase();
        if (['INVENTORY_EXPORT_METADATA', 'SETTINGS', 'LOCATIONS', 'ITEMS'].includes(sectionName)) {
          currentSection = sectionName;
          continue;
        }
      }

      // Skip metadata section data (we don't need to import it)
      if (currentSection === 'INVENTORY_EXPORT_METADATA') {
        continue;
      }

      // Parse settings
      if (currentSection === 'SETTINGS') {
        if (fields.length >= 2 && fields[0] !== 'Setting') {
          const settingName = fields[0];
          const settingValue = fields[1];

          switch (settingName) {
            case 'Expiring Threshold':
              settings!.expiringThreshold = parseInt(settingValue) || 30;
              break;
            case 'Low Stock Threshold':
              settings!.lowStockThreshold = parseInt(settingValue) || 3;
              break;
            case 'Timezone':
              settings!.timezone = settingValue || 'UTC+0';
              break;
            case 'Quick Links':
              try {
                settings!.quickLinks = JSON.parse(settingValue) || [];
              } catch (error) {
                console.error('Error parsing quick links from CSV:', error);
                settings!.quickLinks = [];
              }
              break;
          }
        }
        continue;
      }

      // Parse locations
      if (currentSection === 'LOCATIONS') {
        if (fields[0] === 'ID') {
          // Header row - map columns
          headerMapping = {};
          fields.forEach((header: string, index: number) => {
            const normalizedHeader = header.toLowerCase().trim();
            if (normalizedHeader === 'id') headerMapping.id = index;
            else if (normalizedHeader === 'name') headerMapping.name = index;
            else if (normalizedHeader === 'description') headerMapping.description = index;
            else if (normalizedHeader === 'color') headerMapping.color = index;
            else if (normalizedHeader === 'created at') headerMapping.createdAt = index;
            else if (normalizedHeader === 'updated at') headerMapping.updatedAt = index;
            else if (normalizedHeader === 'created by') headerMapping.createdByUserId = index;
            else if (normalizedHeader === 'updated by') headerMapping.updatedByUserId = index;
          });
        } else if (fields.length >= 2 && headerMapping.id !== undefined) {
          // Data row
          const location: Partial<Location> = {
            id: fields[headerMapping.id] || `location-${uuidv4()}`,
            name: fields[headerMapping.name] || '',
            description: fields[headerMapping.description] || '',
            color: fields[headerMapping.color] || '#888888',
            createdAt: fields[headerMapping.createdAt] || new Date().toISOString(),
            updatedAt: fields[headerMapping.updatedAt] || new Date().toISOString(),
            createdByUserId: fields[headerMapping.createdByUserId] || undefined,
            updatedByUserId: fields[headerMapping.updatedByUserId] || undefined
          };
          locations.push(location);
        }
        continue;
      }

      // Parse items
      if (currentSection === 'ITEMS') {
        if (fields[0] === 'Location Name') {
          // Header row - map columns
          headerMapping = {};
          fields.forEach((header: string, index: number) => {
            const normalizedHeader = header.toLowerCase().trim();
            if (normalizedHeader === 'location name') headerMapping.locationName = index;
            else if (normalizedHeader === 'id') headerMapping.id = index;
            else if (normalizedHeader === 'name') headerMapping.name = index;
            else if (normalizedHeader === 'quantity') headerMapping.quantity = index;
            else if (normalizedHeader === 'unit') headerMapping.unit = index;
            else if (normalizedHeader === 'expiry date') headerMapping.expiryDate = index;
            else if (normalizedHeader === 'description') headerMapping.description = index;
            else if (normalizedHeader === 'watch stock') headerMapping.watchStock = index;
            else if (normalizedHeader === 'created at') headerMapping.createdAt = index;
            else if (normalizedHeader === 'updated at') headerMapping.updatedAt = index;
            else if (normalizedHeader === 'created by') headerMapping.createdByUserId = index;
            else if (normalizedHeader === 'updated by') headerMapping.updatedByUserId = index;
          });
        } else if (fields.length >= 3 && headerMapping.name !== undefined) {
          // Data row
          const locationName = fields[headerMapping.locationName] || '';
          const location = locations.find(loc => loc.name === locationName);

          if (location && fields[headerMapping.name]) {
            const item: ImportItem = {
              id: fields[headerMapping.id] || `item-${uuidv4()}`,
              name: fields[headerMapping.name].trim(),
              quantity: Math.max(1, parseInt(fields[headerMapping.quantity] || '1') || 1),
              unit: (fields[headerMapping.unit] || 'pcs').trim(),
              locationId: location.id,
              description: fields[headerMapping.description] || '',
              expiryDate: fields[headerMapping.expiryDate] || null,
              watchStock: (fields[headerMapping.watchStock] || '').toUpperCase() === 'TRUE',
              createdAt: fields[headerMapping.createdAt] || new Date().toISOString(),
              updatedAt: fields[headerMapping.updatedAt] || new Date().toISOString(),
              createdByUserId: fields[headerMapping.createdByUserId] || undefined,
              updatedByUserId: fields[headerMapping.updatedByUserId] || undefined
            };

            // Parse expiry date if provided
            if (item.expiryDate && item.expiryDate.trim()) {
              const parsedDate = new Date(item.expiryDate);
              if (!isNaN(parsedDate.getTime())) {
                item.expiryDate = parsedDate.toISOString().split('T')[0];
              } else {
                item.expiryDate = null;
              }
            } else {
              item.expiryDate = null;
            }

            items.push(item);
          }
        }
        continue;
      }
    }

    return { items, locations, settings };
  };

  // Parse legacy CSV format (location headers + items)
  const parseLegacyCSV = (lines: string[], items: ImportItem[], locations: Partial<Location>[], settings: ImportData['settings']): ImportData => {
    let currentLocationName = '';
    let isHeaderRow = false;
    let columnMapping: { [key: string]: number } = {};

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Skip empty lines
      if (!line) continue;

      const fields = parseCSVRow(line);

      // Check if this is a location header (single field, not a header row)
      if (fields.length === 1 && fields[0] && fields[0].trim() && !fields[0].toLowerCase().includes('name')) {
        currentLocationName = fields[0].trim();
        isHeaderRow = true;

        // Add location if it doesn't exist
        if (!locations.find(loc => loc.name === currentLocationName)) {
          locations.push({
            id: `location-${uuidv4()}`,
            name: currentLocationName,
            description: `Imported location: ${currentLocationName}`,
            color: '#888888',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          });
        }
        continue;
      }

      // Check if this is a header row
      if (isHeaderRow || (fields.length > 1 && fields[0].toLowerCase().includes('name'))) {
        // Map column headers to indices
        columnMapping = {};
        fields.forEach((header: string, index: number) => {
          const normalizedHeader = header.toLowerCase().trim();
          if (normalizedHeader.includes('name')) columnMapping.name = index;
          else if (normalizedHeader.includes('quantity')) columnMapping.quantity = index;
          else if (normalizedHeader.includes('unit')) columnMapping.unit = index;
          else if (normalizedHeader.includes('expiry') || normalizedHeader.includes('expire')) columnMapping.expiryDate = index;
          else if (normalizedHeader.includes('description')) columnMapping.description = index;
          else if (normalizedHeader.includes('category')) columnMapping.category = index;
          else if (normalizedHeader.includes('brand')) columnMapping.brand = index;
          else if (normalizedHeader.includes('price') || normalizedHeader.includes('cost')) columnMapping.price = index;
          else if (normalizedHeader.includes('barcode')) columnMapping.barcode = index;
          else if (normalizedHeader.includes('stock') && normalizedHeader.includes('watch')) columnMapping.watchStock = index;
        });
        isHeaderRow = false;
        continue;
      }

      // Parse item data
      if (fields.length > 1 && currentLocationName) {
        const location = locations.find(loc => loc.name === currentLocationName);

        if (location && fields[columnMapping.name || 0] && fields[columnMapping.name || 0].trim()) {
          const itemName = fields[columnMapping.name || 0].trim();
          const quantityValue = fields[columnMapping.quantity || 1] || '1';
          const unitValue = fields[columnMapping.unit || 2] || 'pcs';

          const item: ImportItem = {
            id: `item-${uuidv4()}`,
            name: itemName,
            quantity: Math.max(1, parseInt(quantityValue) || 1), // Ensure quantity is at least 1
            unit: unitValue.trim() || 'pcs',
            locationId: location.id,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            watchStock: false
          };

          // Optional fields
          if (columnMapping.expiryDate !== undefined && fields[columnMapping.expiryDate]) {
            const expiryValue = fields[columnMapping.expiryDate];
            // Try to parse various date formats
            const parsedDate = new Date(expiryValue);
            if (!isNaN(parsedDate.getTime())) {
              item.expiryDate = parsedDate.toISOString().split('T')[0];
            }
          }

          if (columnMapping.description !== undefined && fields[columnMapping.description]) {
            item.description = fields[columnMapping.description];
          }

          if (columnMapping.category !== undefined && fields[columnMapping.category]) {
            item.category = fields[columnMapping.category];
          }

          if (columnMapping.brand !== undefined && fields[columnMapping.brand]) {
            item.brand = fields[columnMapping.brand];
          }

          if (columnMapping.price !== undefined && fields[columnMapping.price]) {
            const price = parseFloat(fields[columnMapping.price]);
            if (!isNaN(price)) {
              item.price = price;
            }
          }

          if (columnMapping.barcode !== undefined && fields[columnMapping.barcode]) {
            item.barcode = fields[columnMapping.barcode];
          }

          if (columnMapping.watchStock !== undefined && fields[columnMapping.watchStock]) {
            const watchStockValue = fields[columnMapping.watchStock].toLowerCase();
            item.watchStock = watchStockValue === 'true' || watchStockValue === 'yes' || watchStockValue === '1';
          }

          items.push(item);
        }
      }
    }

    return { items, locations, settings };
  };

  // Handles the import button click. Reads and parses the selected JSON or CSV file, then opens a preview modal.
  const handleImport = () => {
    if (!selectedFile) return;

    const fileExtension = selectedFile.name.toLowerCase().split('.').pop();
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const fileContent = e.target?.result as string;
        let data: ImportData;

        if (fileExtension === 'csv') {
          // Parse CSV file
          data = parseCSV(fileContent);

          if (!data.items || data.items.length === 0) {
            toast.error("Import Failed", {
              description: "No valid items found in CSV file. Please check the format."
            });
            return;
          }
        } else {
          // Parse JSON file
          data = JSON.parse(fileContent);
        }

        setImportData(data);
        setShowPreviewModal(true);
      } catch (err) {
        console.error('Import error:', err);
        toast.error("Import Failed", {
          description: fileExtension === 'csv'
            ? "Invalid CSV file format. Please check your file structure."
            : "Invalid JSON file format"
        });
      }
    };

    reader.readAsText(selectedFile);
  };

  // Validates imported data for structure, required fields, and data integrity.
  const validateData = (data: ImportData) => {
    const errors: ValidationError[] = [];

    // Validate locations
    data.locations?.forEach(location => {
      const validation = validateLocationFields(location);
      if (!validation.isValid) {
        errors.push({
          name: location.name || 'Unknown',
          error: validation.error,
          type: 'location'
        });
      }
    });

    // Validate items
    data.items?.forEach(item => {
      const validation = validateItemFields(item);
      if (!validation.isValid) {
        errors.push({
          name: item.name || 'Unknown',
          error: validation.error,
          type: 'item'
        });
      }
    });

    return errors;
  };

  // Checks imported data for duplicate entries and missing locations, returning issues for user review.
  const checkDuplicatesAndLocations = (data: ImportData) => {
    const duplicates = {
      items: data.items?.filter(item =>
        items.some(existing => existing.name === item.name)) || [],
      locations: data.locations?.filter(location =>
        locations.some(existing => existing.name === location.name)) || []
    };

    const itemsWithMissingLocations = data.items?.filter(item =>
      !locations.some(loc => loc.id === item.locationId) &&
      !data.locations?.some(loc => loc.id === item.locationId)
    ) || [];

    return { duplicates, itemsWithMissingLocations };
  };

  // Handle preview confirmation
  const handlePreviewConfirm = () => {
    if (!importData) return;

    const errors = validateData(importData);
    if (errors.length > 0) {
      setValidationErrors(errors);
      setShowValidationModal(true);
      setShowPreviewModal(false);
      return;
    }

    const { duplicates, itemsWithMissingLocations } = checkDuplicatesAndLocations(importData);

    if (duplicates.items.length > 0 || duplicates.locations.length > 0) {
      setShowDuplicateModal(true);
      setShowPreviewModal(false);
      return;
    }

    if (itemsWithMissingLocations.length > 0) {
      setItemsNeedingLocation(itemsWithMissingLocations);
      setShowLocationModal(true);
      setShowPreviewModal(false);
      return;
    }

    processImport();
  };

  // Generates a unique name for imported items to avoid name collisions.
  const getUniqueItemName = (baseName: string): string => {
    let counter = 2;
    let newName = baseName;
    while (items.some(item => item.name === newName)) {
      newName = `${baseName}_${counter}`;
      counter++;
    }
    return newName;
  };

  // Generates a unique name for imported locations to avoid name collisions.
  const getUniqueLocationName = (baseName: string): string => {
    let counter = 2;
    let newName = baseName;
    while (locations.some(location => location.name === newName)) {
      newName = `${baseName}_${counter}`;
      counter++;
    }
    return newName;
  };

  // Handle location assignment
  const handleLocationAssignment = (itemId: string, locationId: string) => {
    setLocationAssignments(prev => ({
      ...prev,
      [itemId]: locationId
    }));
  };

  // Confirms location assignments and proceeds with import.
  const handleLocationAssignmentConfirm = () => {
    setShowLocationModal(false);
    processImport();
  };

  // --- Import Processing Helpers ---

  const processLocationImport = (locationData: ImportData['locations'], existingLocations: Location[], duplicateAction: 'overwrite' | 'rename'): Map<string, string> => {
    const locationIdMap = new Map<string, string>();
    if (!locationData || locationData.length === 0) return locationIdMap;

    locationData.forEach(location => {
      const existingLocation = existingLocations.find(l => l.name === location.name);

      if (existingLocation && duplicateAction === 'overwrite') {
        const updatedLocation = {
          ...location,
          id: existingLocation.id,
          createdAt: location.createdAt || existingLocation.createdAt,
          updatedAt: new Date().toISOString()
        };
        // Type assertion to satisfy TypeScript
        addLocation(updatedLocation as Omit<Location, 'updatedAt'>, true);
        locationIdMap.set(location.id, existingLocation.id);
      } else if (!existingLocation || duplicateAction === 'rename') {
        const locationName = duplicateAction === 'rename' && existingLocation
          ? getUniqueLocationName(location.name)
          : location.name;
        const newId = `location-${uuidv4()}`;
        const newLocation = {
          ...location,
          name: locationName,
          id: newId,
          createdAt: location.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        // Type assertion to satisfy TypeScript
        addLocation(newLocation as Omit<Location, 'id' | 'createdAt' | 'updatedAt'>);
        locationIdMap.set(location.id, newId);
      }
    });
    return locationIdMap;
  };

  const resolveItemLocationId = (item: ImportItem, locationIdMap: Map<string, string>, userAssignments: Record<string, string>, existingLocations: Location[]): string | undefined => {
    if (userAssignments[item.id]) {
      return userAssignments[item.id];
    }
    if (item.locationId && locationIdMap.has(item.locationId)) {
      return locationIdMap.get(item.locationId);
    }
    if (!item.locationId && item.locationName) {
      const matchingLocation = existingLocations.find(l => l.name === item.locationName);
      return matchingLocation?.id;
    }
    return item.locationId; // Return original if no mapping found
  };

  const processItemImport = (itemData: ImportData['items'], existingItems: Item[], duplicateAction: 'overwrite' | 'rename', locationIdMap: Map<string, string>, userAssignments: Record<string, string>, existingLocations: Location[]) => {
    if (!itemData || itemData.length === 0) return;

    itemData.forEach(item => {
      const existingItem = existingItems.find(i => i.name === item.name);
      const resolvedLocationId = resolveItemLocationId(item, locationIdMap, userAssignments, existingLocations);

      if (existingItem && duplicateAction === 'overwrite') {
        const updatedItem = {
          ...item,
          id: existingItem.id,
          locationId: resolvedLocationId,
          createdAt: item.createdAt || existingItem.createdAt,
          updatedAt: new Date().toISOString(),
          watchStock: item.watchStock || false,
        };
        // Type assertion to satisfy TypeScript
        addItem(updatedItem as Omit<Item, 'updatedAt'>, true);
      } else if (!existingItem || duplicateAction === 'rename') {
        const itemName = duplicateAction === 'rename' && existingItem
          ? getUniqueItemName(item.name)
          : item.name;
        const newItem = {
          ...item,
          name: itemName,
          id: `item-${uuidv4()}`,
          locationId: resolvedLocationId,
          createdAt: item.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          watchStock: item.watchStock || false,
        };
        // Type assertion to satisfy TypeScript
        addItem(newItem as Omit<Item, 'id' | 'createdAt' | 'updatedAt'>);
      }
    });
  };

  const processSettingsImport = (settingsData: ImportData['settings']) => {
    if (settingsData?.expiringThreshold) {
      localStorage.setItem('expiring-threshold', settingsData.expiringThreshold.toString());
    }

    if (settingsData?.lowStockThreshold) {
      localStorage.setItem('low-stock-threshold', settingsData.lowStockThreshold.toString());
    }

    if (settingsData?.quickLinks) {
      localStorage.setItem(QUICK_LINKS_STORAGE_KEY, JSON.stringify(settingsData.quickLinks));
    }

    if (settingsData?.timezone) {
      localStorage.setItem('timezone', settingsData.timezone);
    }
  };

  // Processes the import using helper functions for items, locations, and settings.
  const processImport = () => {
    if (!importData) return;

    try {
      const locationIdMap = processLocationImport(importData.locations, locations, duplicateAction);
      processItemImport(importData.items, items, duplicateAction, locationIdMap, locationAssignments, locations);
      processSettingsImport(importData.settings);

      toast.success("Import Successful", {
        description: `Imported ${importData.items?.length || 0} items and ${importData.locations?.length || 0} locations.`
      });

      // Reset states
      setSelectedFile(null);
      setImportData(null);
      setLocationAssignments({});
      setShowPreviewModal(false);
      setShowDuplicateModal(false);
      setShowLocationModal(false);
    } catch (err) {
      console.error('Import error:', err);
      toast.error("Import Failed", {
        description: "There was an error importing your data. Check the console for details."
      });
    }
  };

  // Handles export of inventory data to JSON or CSV for backup or migration.
  const handleExport = () => {
    // Get quick links from localStorage
    let quickLinks: string[] = [];
    try {
      const savedQuickLinks = localStorage.getItem(QUICK_LINKS_STORAGE_KEY);
      if (savedQuickLinks) {
        quickLinks = JSON.parse(savedQuickLinks);
      }
    } catch (error) {
      console.error('Error parsing quick links for export:', error);
    }

    const data = {
      items,
      locations,
      settings: {
        expiringThreshold: localStorage.getItem('expiring-threshold')
          ? parseInt(localStorage.getItem('expiring-threshold') || '30')
          : 30,
        lowStockThreshold: localStorage.getItem('low-stock-threshold')
          ? parseInt(localStorage.getItem('low-stock-threshold') || '3')
          : 3,
        quickLinks: quickLinks,
        timezone: localStorage.getItem('timezone') || 'UTC+0'
      }
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("Export Successful", {
      description: "Your inventory data has been exported."
    });
  };

  const convertToCSV = (items: Item[], locations: Location[]) => {
    // Create comprehensive CSV content with all data
    let csvContent = '';

    // Add metadata header
    csvContent += '"INVENTORY_EXPORT_METADATA"\n';
    csvContent += '"Export Date","Export Version","Total Items","Total Locations"\n';
    csvContent += `"${new Date().toISOString()}","2.0","${items.length}","${locations.length}"\n\n`;

    // Add settings section
    csvContent += '"SETTINGS"\n';
    csvContent += '"Setting","Value"\n';
    csvContent += `"Expiring Threshold","${localStorage.getItem('expiring-threshold') || '30'}"\n`;
    csvContent += `"Low Stock Threshold","${localStorage.getItem('low-stock-threshold') || '3'}"\n`;
    csvContent += `"Timezone","${localStorage.getItem('timezone') || 'UTC+0'}"\n`;

    // Add quick links
    try {
      const savedQuickLinks = localStorage.getItem(QUICK_LINKS_STORAGE_KEY);
      if (savedQuickLinks) {
        const quickLinks = JSON.parse(savedQuickLinks);
        csvContent += `"Quick Links","${JSON.stringify(quickLinks).replace(/"/g, '""')}"\n`;
      }
    } catch (error) {
      console.error('Error parsing quick links for CSV export:', error);
    }
    csvContent += '\n';

    // Add locations section
    csvContent += '"LOCATIONS"\n';
    csvContent += '"ID","Name","Description","Color","Created At","Updated At","Created By","Updated By"\n';
    locations.forEach(location => {
      const row = [
        location.id,
        location.name,
        location.description || '',
        location.color || '',
        location.createdAt,
        location.updatedAt,
        location.createdByUserId || '',
        location.updatedByUserId || ''
      ].map(cell => `"${String(cell ?? '').replace(/"/g, '""')}"`).join(',');
      csvContent += row + '\n';
    });
    csvContent += '\n';

    // Group items by location for better organization
    const itemsByLocation = locations.map(location => {
      const locationItems = items.filter(item => item.locationId === location.id);
      return {
        location,
        items: locationItems
      };
    }).filter(group => group.items.length > 0); // Only include locations with items

    // Add items section
    csvContent += '"ITEMS"\n';
    csvContent += '"Location Name","ID","Name","Quantity","Unit","Expiry Date","Description","Watch Stock","Created At","Updated At","Created By","Updated By"\n';

    itemsByLocation.forEach(({ location, items }) => {
      items.forEach(item => {
        const row = [
          location.name,
          item.id,
          item.name,
          item.quantity,
          item.unit,
          item.expiryDate ? format(new Date(item.expiryDate), 'yyyy-MM-dd') : '',
          item.description || '',
          item.watchStock ? 'TRUE' : 'FALSE',
          item.createdAt,
          item.updatedAt,
          item.createdByUserId || '',
          item.updatedByUserId || ''
        ].map(cell => `"${String(cell ?? '').replace(/"/g, '""')}"`).join(',');

        csvContent += row + '\n';
      });
    });

    return csvContent;
  };


  const handleExportCSV = () => {
    const csv = convertToCSV(items, locations);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory-export-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("Export Successful", {
      description: "Your inventory data has been exported as CSV."
    });
  };

  return (
    <>
      <div className="space-y-6 animate-slide-in">
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Import Data</CardTitle>
            <CardDescription>
              Import your inventory data from a JSON or CSV file.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input
                type="file"
                accept=".json,.csv"
                onChange={handleFileSelect}
                className="cursor-pointer"
              />
              <Button
                onClick={handleImport}
                disabled={!selectedFile}
                className="w-full sm:w-auto"
              >
                {selectedFile?.name.toLowerCase().endsWith('.csv') ? (
                  <FileText className="mr-2 h-4 w-4" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                Import {selectedFile?.name.toLowerCase().endsWith('.csv') ? 'CSV' : 'JSON'} Data
              </Button>
              <div className="text-sm text-muted-foreground space-y-2">
                <p>Note: Importing data will add to your existing inventory.</p>
                
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full">
          <CardHeader>
            <CardTitle>Export Data</CardTitle>
            <CardDescription>
              Export your inventory data in different formats
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button onClick={handleExport} className="w-full sm:w-auto">
                <FileDown className="mr-2 h-4 w-4" />
                Export to JSON
              </Button>
              <Button
                variant="outline"
                onClick={handleExportCSV}
                className="w-full sm:w-auto"
              >
                <FileDown className="mr-2 h-4 w-4" />
                Export to CSV
              </Button>
            </div>

          </CardContent>
        </Card>

        {/* Reset Database Section has been moved to AdminSettings */}

      </div>

      {/* Import Preview Dialog */}
      <Dialog open={showPreviewModal} onOpenChange={setShowPreviewModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Import Preview</DialogTitle>
            <DialogDescription>
              Review the data to be imported from your {selectedFile?.name.toLowerCase().endsWith('.csv') ? 'CSV' : 'JSON'} file.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-primary">{importData?.items?.length || 0}</div>
                <div className="text-sm text-muted-foreground">Items to import</div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-primary">{importData?.locations?.length || 0}</div>
                <div className="text-sm text-muted-foreground">Locations to import</div>
              </div>
            </div>

            {/* Show sample items */}
            {importData?.items && importData.items.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Sample Items:</h4>
                <div className="max-h-32 overflow-y-auto border rounded p-2 text-sm">
                  {importData.items.slice(0, 5).map((item, index) => (
                    <div key={index} className="flex justify-between py-1">
                      <span>{item.name}</span>
                      <span className="text-muted-foreground">{item.quantity} {item.unit}</span>
                    </div>
                  ))}
                  {importData.items.length > 5 && (
                    <div className="text-muted-foreground italic">...and {importData.items.length - 5} more items</div>
                  )}
                </div>
              </div>
            )}

            {/* Show locations */}
            {importData?.locations && importData.locations.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Locations:</h4>
                <div className="flex flex-wrap gap-2">
                  {importData.locations.map((location, index) => (
                    <span key={index} className="px-2 py-1 bg-primary/10 rounded text-sm">
                      {location.name}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreviewModal(false)}>Cancel</Button>
            <Button onClick={handlePreviewConfirm}>Confirm Import</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Validation Error Dialog */}
      <Dialog open={showValidationModal} onOpenChange={setShowValidationModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Validation Errors</DialogTitle>
            <DialogDescription>
              The following errors were found in the import file. Please correct them and try again.
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-60 overflow-y-auto space-y-2">
            {validationErrors.map((error, index) => (
              <div key={index} className="text-sm p-2 border rounded bg-destructive/10 border-destructive/50 text-destructive">
                <strong>{error.type === 'item' ? 'Item' : 'Location'}:</strong> {error.name} - {error.error}
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button onClick={() => setShowValidationModal(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Duplicate Handling Dialog */}
      <Dialog open={showDuplicateModal} onOpenChange={setShowDuplicateModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Duplicate Items/Locations Found</DialogTitle>
            <DialogDescription>
              Some items or locations in the import file already exist in your inventory. How would you like to handle duplicates?
            </DialogDescription>
          </DialogHeader>
          <RadioGroup value={duplicateAction} onValueChange={(value: 'overwrite' | 'rename') => setDuplicateAction(value)}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="overwrite" id="overwrite" />
              <Label htmlFor="overwrite">Overwrite existing entries</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="rename" id="rename" />
              <Label htmlFor="rename">Rename new entries (e.g., Item_2)</Label>
            </div>
          </RadioGroup>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDuplicateModal(false)}>Cancel</Button>
            <Button onClick={() => {
              setShowDuplicateModal(false);
              // Re-check for location issues after deciding on duplicates
              if (importData) {
                 const { itemsWithMissingLocations } = checkDuplicatesAndLocations(importData);
                 if (itemsWithMissingLocations.length > 0) {
                   setItemsNeedingLocation(itemsWithMissingLocations);
                   setShowLocationModal(true);
                 } else {
                   processImport();
                 }
              }
            }}>Continue Import</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Missing Location Assignment Dialog */}
      <Dialog open={showLocationModal} onOpenChange={setShowLocationModal}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Assign Locations</DialogTitle>
            <DialogDescription>
              Some imported items reference locations that don't exist. Please assign them to an existing location.
            </DialogDescription>
          </DialogHeader>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item Name</TableHead>
                <TableHead>Assign to Location</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {itemsNeedingLocation.map(item => (
                <TableRow key={item.id}>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>
                    <Select
                      onValueChange={(value) => handleLocationAssignment(item.id, value)}
                      value={locationAssignments[item.id] || ""}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select location..." />
                      </SelectTrigger>
                      <SelectContent>
                        {locations.map(location => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLocationModal(false)}>Cancel Import</Button>
            <Button
              onClick={handleLocationAssignmentConfirm}
              disabled={Object.keys(locationAssignments).length !== itemsNeedingLocation.length}
            >
              Confirm Assignments & Import
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset Confirmation Dialog has been moved to AdminSettings */}
    </>
  );
};

export default ImportExportSettings;
import * as React from "react"

/**
 * Breakpoint threshold for mobile devices in pixels
 */
const MO<PERSON>LE_BREAKPOINT = 768

/**
 * Custom hook to detect if the viewport is mobile-sized
 * 
 * This hook uses media queries to detect if the current viewport width
 * is below the mobile breakpoint (768px). It updates on window resize.
 * 
 * @returns {boolean} True if viewport is mobile-sized, false otherwise
 */
export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}

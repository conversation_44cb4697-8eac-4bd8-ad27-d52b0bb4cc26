/**
 * ui.ts
 *
 * Centralizes UI-related constants for component defaults, layout, and animation.
 * Ensures consistency and maintainability for all UI elements and styles.
 */

// Layout measurements for the application's UI components
export const LAYOUT = {
  // Width of the sidebar in pixels
  SIDEBAR_WIDTH: 240,
  // Height of the header in pixels
  HEADER_HEIGHT: 64,
  // Height of the footer in pixels
  FOOTER_HEIGHT: 40,
  CONTENT_MAX_WIDTH: 1200,
  MOBILE_BREAKPOINT: 768,
  TABLET_BREAKPOINT: 1024,
  CARD_MIN_WIDTH: 300,
  TABLE_ROW_HEIGHT: 56,
  GRID_GAP: 16,
  DEFAULT_PADDING: 16,
  MODAL_WIDTH: {
    SM: 400,
    MD: 600,
    LG: 800,
    XL: 1000
  }
};

// Animation durations
export const ANIMATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  TOAST_DURATION: 5000,
  TRANSITION: '0.3s ease'
};

// Z-index layers
export const Z_INDEX = {
  BACKGROUND: -1,
  BASE: 0,
  CONTENT: 10,
  HEADER: 100,
  SIDEBAR: 100,
  MODAL: 1000,
  DROPDOWN: 1000,
  TOOLTIP: 1500,
  TOAST: 2000
};

// Typography
export const TYPOGRAPHY = {
  FONT_FAMILY: {
    SANS: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, sans-serif',
    MONO: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace'
  },
  FONT_SIZE: {
    XS: '0.75rem',   // 12px
    SM: '0.875rem',  // 14px
    BASE: '1rem',    // 16px
    LG: '1.125rem',  // 18px
    XL: '1.25rem',   // 20px
    XXL: '1.5rem',   // 24px
    XXXL: '1.875rem' // 30px
  },
  LINE_HEIGHT: {
    TIGHT: '1.25',
    NORMAL: '1.5',
    LOOSE: '1.75'
  }
};

// Common colors (for dynamic styles, theme colors are handled by Tailwind)
export const COLORS = {
  STATUS: {
    SUCCESS: 'hsl(var(--success))',
    ERROR: 'hsl(var(--destructive))',
    WARNING: 'hsl(var(--warning))',
    INFO: 'hsl(var(--info))'
  }
};

// Icon sizes
export const ICON_SIZE = {
  SM: 16,
  MD: 20,
  LG: 24,
  XL: 32
};

// Default table settings
export const TABLE_DEFAULTS = {
  PAGE_SIZES: [10, 25, 50, 100],
  DEFAULT_PAGE_SIZE: 10,
  SORT_DIRECTIONS: ['asc', 'desc'] as const
}; 
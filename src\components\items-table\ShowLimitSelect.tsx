import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const DEFAULT_OPTIONS = [10, 20, 50];

interface ShowLimitSelectProps {
  value: number;
  onChange: (value: number) => void;
  options?: number[];
  showAllOption?: boolean;
  totalItems?: number;
  className?: string;
  disabled?: boolean;
}

const ShowLimitSelect = ({
  value,
  onChange,
  options = DEFAULT_OPTIONS,
  showAllOption = true,
  totalItems,
  className = "w-[80px]",
  disabled = false
}: ShowLimitSelectProps) => {
  const handleValueChange = (value: string) => {
    if (value === 'all') {
      onChange(totalItems || 999999);
    } else {
      onChange(Number(value));
    }
  };

  const isAllSelected = showAllOption && value === (totalItems || 999999);
  const currentValue = isAllSelected ? 'all' : value.toString();

  return (
    <div className={`flex items-center ${className}`}>
      <span className="mr-2 text-sm text-muted-foreground whitespace-nowrap">Show:</span>
      <Select
        value={currentValue}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger className="w-full">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {showAllOption && (
            <SelectItem value="all">All</SelectItem>
          )}
          {options.map(option => (
            <SelectItem key={option} value={option.toString()}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default ShowLimitSelect;
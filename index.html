<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Imbentory - Inventory Management</title>
    <link rel="icon" href="/favicon.ico" />
  </head>

  <body>
    <div id="root"></div>
    <script type="text/javascript">
      // Initialize app configuration
      window.APP_CONFIG = {
        name: 'imbentory',
        version: '1.0.0',
        storagePrefix: 'imbentory-',
      };

      // Setup error handling
      window.onerror = function(msg, url, lineNo, columnNo, error) {
        console.error('Error: ' + msg + '\nURL: ' + url + '\nLine: ' + lineNo + '\nColumn: ' + columnNo + '\nError object: ' + JSON.stringify(error));
        return false;
      };

      // Check local storage availability
      try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
      } catch (e) {
        console.error('LocalStorage is not available. Application may not function correctly.');
      }
    </script>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      // Register service worker for IndexedDB migrations
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/background.js')
            .then(registration => {
              console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(error => {
              console.error('Service Worker registration failed:', error);
            });
        });
      }
    </script>
  </body>
</html>

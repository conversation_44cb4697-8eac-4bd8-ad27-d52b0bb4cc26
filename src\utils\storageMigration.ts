/**
 * Handles migration of storage data between versions.
 * This should be run at app startup (e.g., in main.tsx) before any storage-dependent logic.
 *   - allItems.expiryFilter
 *   - allItems.itemsPerPage
 *
 * If a value is not valid JSON, the key is removed (reset to default).
 * You can extend this list as needed.
 */

const keysToCheck = [
  "allItems.sortField",
  "allItems.sortDirection",
  "allItems.filterLocation",
  "allItems.expiryFilter",
  "allItems.itemsPerPage",
];

export function runStorageMigration() {
  if (typeof window === "undefined" || !window.localStorage) return;

  keysToCheck.forEach((key) => {
    const value = window.localStorage.getItem(key);
    if (value === null) return; // Key not present, nothing to do

    try {
      // Try to parse as JSON; if it works, value is fine
      JSON.parse(value);
    } catch {
      // If parsing fails, value is not valid JSON; remove the key
      console.warn(`[storageMigration] Removing invalid localStorage key "${key}" with value:`, value);
      window.localStorage.removeItem(key);
    }
  });
}
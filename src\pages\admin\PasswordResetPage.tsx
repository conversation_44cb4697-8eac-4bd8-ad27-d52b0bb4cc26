import React, { useState, useCallback } from 'react';
// Layout is now applied centrally in App.tsx, so this page is rendered without an extra wrapper.
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import BackButton from '@/components/ui/BackButton';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import * as authUtils from '@/utils/authUtils';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils'; // Utility for conditional class name merging

const PasswordResetPage: React.FC = () => {
  const { user: adminUser } = useAuth(); // Get the currently logged-in admin user
  const [targetUsername, setTargetUsername] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    if (!adminUser) return;

    setError(null);
    if (!targetUsername.trim()) {
      setError("Target username is required.");
      return;
    }
    if (newPassword.length < 6) {
      setError("New password must be at least 6 characters long.");
      return;
    }
    if (newPassword !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }

    setIsLoading(true);
    try {
      const success = await authUtils.resetPasswordByAdmin(
        adminUser,
        targetUsername.trim(),
        newPassword
      );

      if (success) {
        toast({ title: "Password Reset Successful", description: `Password for user "${targetUsername.trim()}" has been reset.` });
        // Clear form on success
        setTargetUsername('');
        setNewPassword('');
        setConfirmPassword('');
      } else {
        // Error toast likely shown by resetPasswordByAdmin
        setError(`Failed to reset password. User "${targetUsername.trim()}" might not exist.`);
      }
    } catch (err) {
      console.error("Password reset error:", err);
      setError("An unexpected error occurred during password reset.");
    } finally {
      setIsLoading(false);
    }
  }, [adminUser, targetUsername, newPassword, confirmPassword]);

  return (
    // Layout wrapper removed
      <div className="space-y-6 max-w-xl mx-auto animate-slide-in">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reset User Password</h1>
            <p className="text-muted-foreground">Reset the password for an existing user account.</p>
          </div>
          <BackButton />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Password Reset Form</CardTitle>
            <CardDescription>
              Enter the username of the account and the new password.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="target-username">Target Username</Label>
                <Input
                  id="target-username"
                  type="text"
                  placeholder="Username of the account to reset"
                  value={targetUsername}
                  onChange={(e) => setTargetUsername(e.target.value)}
                  required
                  className={cn(error?.includes("username") || error?.includes("User") ? 'border-destructive' : '')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-password">New Password</Label>
                <Input
                  id="new-password"
                  type="password"
                  placeholder="Enter new password (min. 6 characters)"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  className={cn(error?.includes("password") || error?.includes("match") ? 'border-destructive' : '')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirm-password">Confirm New Password</Label>
                <Input
                  id="confirm-password"
                  type="password"
                  placeholder="Confirm the new password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className={cn(error?.includes("match") ? 'border-destructive' : '')}
                />
              </div>

              {error && (
                <p className="text-sm text-destructive">{error}</p>
              )}

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Resetting Password...' : 'Reset Password'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    // Layout wrapper removed
  );
};

export default PasswordResetPage;
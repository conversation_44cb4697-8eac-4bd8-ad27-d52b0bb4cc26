import React from "react";
import { Package, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";

interface EmptyStateProps {
  title: string;
  description: string;
  buttonText?: string;
  buttonAction?: () => void;
  icon?: React.ReactNode;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  buttonText,
  buttonAction,
  icon,
}) => (
  <div className="flex flex-col items-center justify-center py-10 px-4 text-center border border-border rounded-lg bg-background">
    <div className="rounded-full bg-muted p-3">
      {icon ?? <Package className="h-6 w-6 text-muted-foreground" />}
    </div>
    <h3 className="mt-4 text-lg font-semibold">{title}</h3>
    <p className="mt-2 text-sm text-muted-foreground max-w-sm">{description}</p>
    {buttonText && buttonAction && (
      <Button className="mt-6" onClick={buttonAction}>
        <Plus className="h-4 w-4 mr-2" />
        {buttonText}
      </Button>
    )}
  </div>
);

export default EmptyState;

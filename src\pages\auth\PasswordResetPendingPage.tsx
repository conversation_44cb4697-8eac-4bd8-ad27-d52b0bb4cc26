import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardT<PERSON>le, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { BookHeart, KeyRound, LogOut } from 'lucide-react';
import { toast } from 'sonner';

/**
 * PasswordResetPendingPage Component
 *
 * Informs the user that their password reset request is pending admin approval.
 * Provides feedback and a logout button to return to the login screen.
 */
const PasswordResetPendingPage: React.FC = () => {
  const { logout } = useAuth();

  // --- Handlers ---
  // Log out the user and show feedback
  const handleLogout = () => {
    logout();
    toast.info("Logged Out", {
      description: "You have been logged out successfully."
    });
  };

  // --- Rendering ---
  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-md space-y-8">
        {/* App Title and Description Section */}
        <div className="flex flex-col items-center text-center">
          <BookHeart className="h-12 w-12 text-primary" />
          <h1 className="mt-2 text-2xl font-bold">Imbentorys</h1>
          <p className="text-sm text-muted-foreground">Password Reset Pending</p>
        </div>

        {/* Pending Reset Card */}
        <Card className="w-full">
          <CardHeader>
            <div className="flex items-center justify-center mb-4">
              <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
                <KeyRound className="h-8 w-8 text-blue-600" />
              </div>
            </div>
            <CardTitle className="text-center">Password Reset Pending</CardTitle>
            <CardDescription className="text-center">
              Your password reset request is being processed
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-muted p-4 rounded-md text-sm">
              <p className="mb-2">Your password reset request has been submitted and is waiting for administrator approval.</p>
              <p>An administrator will reset your password and provide you with your new login credentials.</p>
            </div>
            <div className="flex justify-center">
              <Button 
                variant="outline" 
                className="flex items-center gap-2"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4" />
                Return to Login
              </Button>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <p className="text-xs text-muted-foreground">
              If you need immediate assistance, please contact your system administrator.
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default PasswordResetPendingPage;

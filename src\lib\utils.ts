// Utility imports for class name merging and Tailwind CSS integration
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Utility function to merge class names conditionally and handle Tailwind CSS conflicts.
 * Combines class names using clsx and then merges them with tailwind-merge for conflict resolution.
 *
 * @param {...ClassValue[]} inputs - List of class values (strings, arrays, objects)
 * @returns {string} - Merged class name string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

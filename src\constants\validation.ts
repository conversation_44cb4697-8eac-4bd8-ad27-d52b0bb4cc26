/**
 * validation.ts
 *
 * Centralizes all form validation rules, limits, and messages for consistency.
 * Use these constants to enforce uniform validation logic throughout the app.
 */

// Maximum allowed lengths for text fields to prevent excessive input
export const MAX_NAME_LENGTH = 100; // e.g., item names, labels
export const MAX_DESCRIPTION_LENGTH = 500; // e.g., item descriptions, notes
export const MAX_TAG_LENGTH = 50; // e.g., keywords, categories
export const MAX_NOTES_LENGTH = 1000; // e.g., additional information, comments
export const MAX_LOCATION_NAME_LENGTH = 100; // e.g., warehouse, storage locations
export const MAX_CUSTOM_FIELD_LENGTH = 200; // e.g., user-defined fields
export const MAX_URL_LENGTH = 2048; // e.g., links, web addresses

// Numeric constraints
export const MIN_QUANTITY = 0;
export const MAX_QUANTITY = 999999;
export const MIN_PRICE = 0;
export const MAX_PRICE = 9999999.99;
export const PRICE_DECIMAL_PLACES = 2;

// Form validation messages
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  TOO_LONG: (max: number) => `Must be ${max} characters or less`,
  TOO_SHORT: (min: number) => `Must be at least ${min} characters`,
  INVALID_FORMAT: 'Invalid format',
  MIN_VALUE: (min: number) => `Must be at least ${min}`,
  MAX_VALUE: (max: number) => `Must be ${max} or less`,
  INVALID_DATE: 'Invalid date',
  FUTURE_DATE: 'Date cannot be in the future',
  DUPLICATE: 'This value already exists',
  INVALID_URL: 'Invalid URL format',
  INVALID_EMAIL: 'Invalid email address',
  PASSWORD_STRENGTH: 'Password must be at least 8 characters with a number and special character',
  PASSWORDS_DONT_MATCH: 'Passwords do not match'
};

// Input patterns
export const PATTERNS = {
  EMAIL: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
  URL: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
  ALPHANUMERIC: /^[a-zA-Z0-9]*$/,
  NUMBER: /^[0-9]*$/,
  DECIMAL: /^\d*(\.\d+)?$/,
  DATE: /^\d{4}-\d{2}-\d{2}$/
};

// Batch operations
export const MAX_BATCH_ITEMS = 100;
export const MAX_IMPORT_SIZE = 1024 * 1024 * 5; // 5MB 
import React, { useState } from 'react'; // Import useState
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { AlertTriangle, TestTubeDiagonal } from 'lucide-react';
import { useInventory } from '@/context/InventoryContext'; // Import useInventory
import { toast } from 'sonner'; // Use Sonner toast
import { v4 as uuidv4 } from 'uuid';
import { useAuth } from '@/context/AuthContext';
import { User } from '@/types';
import { hashPassword } from '@/utils/authUtils';
import { saveUsers } from '@/utils/storageUtils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"; // Import AlertDialog components

import adminConfig from '@/config/admin.config';

const AdminSettings: React.FC = () => {
  const { clearAllData } = useInventory();
  const { user: adminUser, logout } = useAuth();
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  // Show the confirmation dialog when the reset button is clicked.
  const handleResetClick = () => {
    setShowResetConfirm(true);
  };

  const handleResetConfirm = async () => {
    // Ensure the current user is an admin before proceeding with the reset.
    if (!adminUser || adminUser.role !== 'admin') return;

    setIsResetting(true);

    try {
      // Only clear inventory and location data; do NOT touch users.
      clearAllData();
      setShowResetConfirm(false);
      toast.success("Database Reset", {
        description: "All inventory and location data has been cleared. User accounts are preserved.",
      });
    } catch (error) {
      // Log any errors that occur during the reset process.
      console.error("Error during reset:", error);

      // Display an error toast notification to the user.
      toast.error("Reset Failed", {
        description: "An error occurred while resetting the database.",
      });
    } finally {
      setIsResetting(false);
    }
  };

  const { user: authUser } = useAuth();

  if (!authUser || authUser.username !== "admin") {
    return null;
  }

  return (
    <div className="space-y-6 animate-slide-in">
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Admin Actions</CardTitle>
          <CardDescription>
            Perform administrative tasks and access developer tools. Use with caution.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">

          {/* Link to performance testing page for evaluating app speed and responsiveness. */}
          <div className="p-4 border border-border/50 rounded-md">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <TestTubeDiagonal className="h-5 w-5" />
              Performance Testing
            </h3>
            <p className="text-sm text-muted-foreground mt-1 mb-3">
              Access the performance testing page to evaluate application speed and responsiveness under load.
            </p>
            <Button asChild variant="outline" className="w-full sm:w-auto">
              <Link to="/performance-test">Go to Performance Test Page</Link>
            </Button>
          </div>

          {/* Section for resetting all database data except the admin account. */}
          <div className="p-4 border border-destructive/50 rounded-md bg-destructive/10">
            <h3 className="text-lg font-semibold text-destructive flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Reset Database
            </h3>
            <p className="text-sm text-destructive/90 mt-1 mb-3">
              This action will permanently delete all items and locations. All user accounts will be preserved. This cannot be undone.
            </p>
            <Button
              variant="destructive"
              onClick={handleResetClick}
              disabled={isResetting}
              className="w-full sm:w-auto"
            >
              {isResetting ? "Resetting..." : "Reset Database"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Confirmation dialog for irreversible database reset. */}
      <AlertDialog open={showResetConfirm} onOpenChange={setShowResetConfirm}>
        <AlertDialogContent className="max-w-md mx-auto">
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete all your current items and locations.
              <b>User accounts will be preserved and not affected.</b>
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0">
            <AlertDialogCancel disabled={isResetting} className="w-full sm:w-auto">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleResetConfirm}
              disabled={isResetting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90 w-full sm:w-auto"
            >
              {isResetting ? "Resetting..." : "Yes, Reset Database"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminSettings;
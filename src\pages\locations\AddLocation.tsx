import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useInventory } from '@/context/InventoryContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { validateLocationName, validateLocationFields } from "@/utils/formUtils";
import { useFormValidation } from "@/hooks/useFormValidation";
import { Location } from "@/types";
import BackButton from '@/components/ui/BackButton';

const AddLocation = () => {
  const { addLocation, locations } = useInventory();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [color, setColor] = useState('#888888');
  const navigate = useNavigate();

  const { error, validate, clearError } = useFormValidation();

  // Handlers for form field changes
  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
    validate(() => validateLocationName(newName, locations));
  }, [locations, validate]);

  const handleDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDescription(e.target.value);
  }, []);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();

    const newLocation: Omit<Location, 'id' | 'createdAt' | 'updatedAt'> & { color?: string } = {
      name: name.trim(),
      description: description.trim(),
      color: color,
    };

    // Validate all fields before submission
    const nameValidation = validate(() => validateLocationName(name, locations));
    const fieldsValidation = validate(() => validateLocationFields(newLocation));

    if (!nameValidation || !fieldsValidation) return;

    try {
      addLocation(newLocation);
      navigate(-1);
    } catch (err) {
      if (err instanceof Error) {
        validate(() => ({ isValid: false, error: err.message }));
      }
    }
  }, [name, description, color, locations, validate, addLocation, navigate]);

  return (
    <div className="space-y-6 animate-slide-in">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">Add Location</h1>
            <p className="text-muted-foreground">
              Create a new storage location for your inventory items.
            </p>
          </div>
        </div>

        <Card className="max-w-2xl mr-auto">
          <CardHeader>
            <CardTitle>Location Information</CardTitle>
            <CardDescription>
              Create a new location to organize your inventory.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-sm">
            <form onSubmit={handleSubmit} className="flex flex-col gap-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Location Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={handleNameChange}
                    placeholder="Enter location name"
                    className={error ? 'border-destructive' : ''}
                    required
                  />
                  {error && (
                    <p className="text-sm text-destructive">{error}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={handleDescriptionChange}
                    placeholder="Enter location description"
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">Location Color</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="color"
                      type="color"
                      value={color}
                      onChange={(e) => setColor(e.target.value)}
                      className="p-1 h-10 w-14 block border border-input rounded-md cursor-pointer"
                    />
                    <span className="text-sm text-muted-foreground">{color}</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-4">
                <Button variant="outline" onClick={() => navigate(-1)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={!!error || !name.trim()}>
                  Create Location
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        <div className="flex gap-2">
          <BackButton />
        </div>
      </div>
  );
};

export default AddLocation;
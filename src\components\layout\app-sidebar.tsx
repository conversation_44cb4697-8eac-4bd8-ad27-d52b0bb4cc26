import { Link } from 'react-router-dom';
import { BookHeart } from 'lucide-react';
import { NavMain } from '@/components/layout/nav-main';
import { NavSecondary } from '@/components/layout/nav-secondary';
import { NavUser } from '@/components/layout/nav-user';
import { Sidebar, SidebarFooter, SidebarHeader } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';

// Sidebar component containing navigation and user info for the app.
export function AppSidebar() {
  return (
    <Sidebar variant="inset">
      <SidebarHeader className='border-b border-border/20 pb-1'>
        <Link to="/" className="flex items-center gap-2 px-4 py-3">
          <BookHeart className="h-5 w-5" />
          <span className="font-bold text-xl">Imbentory App</span>
        </Link>
      </SidebarHeader>

      <div className="flex-1 overflow-auto py-2">
        <NavMain />
        <NavSecondary />
      </div>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
} 
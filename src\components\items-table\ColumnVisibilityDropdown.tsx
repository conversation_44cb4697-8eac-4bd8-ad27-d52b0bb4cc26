import { Button } from '@/components/ui/button';
import { Columns } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLocalStorage } from '@/hooks/useLocalStorage';

export interface ColumnVisibility {
  name: boolean;
  quantity: boolean;
  location: boolean;
  expiryDate: boolean;
  description: boolean;
  createdAt: boolean;
  updatedAt: boolean;
  createdBy: boolean;
  updatedBy: boolean;
}

const defaultVisibility: ColumnVisibility = {
  name: true,
  quantity: true,
  location: true,
  expiryDate: true,
  description: true,
  createdAt: false,
  updatedAt: false,
  createdBy: false,
  updatedBy: false,
};

interface ColumnVisibilityDropdownProps {
  showLocationColumn?: boolean;
}

const ColumnVisibilityDropdown = ({ showLocationColumn = true }: ColumnVisibilityDropdownProps) => {
  const [columnVisibility, setColumnVisibility] = useLocalStorage<ColumnVisibility>(
    'allItems.columnVisibility',
    defaultVisibility
  );

  const toggleColumn = (column: keyof ColumnVisibility) => {
    setColumnVisibility(prev => ({
      ...prev,
      [column]: !prev[column]
    }));
  };

  // Prevent hiding all columns by disabling the last visible column's checkbox.
  const isDisabled = (column: keyof ColumnVisibility) => {
    const visibleCount = Object.values(columnVisibility).filter(Boolean).length;
    return columnVisibility[column] && visibleCount <= 2;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 gap-1">
          <Columns className="h-4 w-4" />
          <span className="hidden sm:inline">Columns</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[180px]">
        <DropdownMenuLabel>Toggle Columns</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuCheckboxItem
          checked={columnVisibility.name}
          onCheckedChange={() => toggleColumn('name')}
          disabled={isDisabled('name')}
        >
          Name
        </DropdownMenuCheckboxItem>

        <DropdownMenuCheckboxItem
          checked={columnVisibility.quantity}
          onCheckedChange={() => toggleColumn('quantity')}
          disabled={isDisabled('quantity')}
        >
          Quantity
        </DropdownMenuCheckboxItem>

        {showLocationColumn && (
          <DropdownMenuCheckboxItem
            checked={columnVisibility.location}
            onCheckedChange={() => toggleColumn('location')}
            disabled={isDisabled('location')}
          >
            Location
          </DropdownMenuCheckboxItem>
        )}

        <DropdownMenuCheckboxItem
          checked={columnVisibility.expiryDate}
          onCheckedChange={() => toggleColumn('expiryDate')}
          disabled={isDisabled('expiryDate')}
        >
          Expiry Date
        </DropdownMenuCheckboxItem>

        <DropdownMenuCheckboxItem
          checked={columnVisibility.description}
          onCheckedChange={() => toggleColumn('description')}
          disabled={isDisabled('description')}
        >
          Description
        </DropdownMenuCheckboxItem>

        <DropdownMenuSeparator />
        <DropdownMenuLabel className="text-xs text-muted-foreground">Additional Info</DropdownMenuLabel>

        <DropdownMenuCheckboxItem
          checked={columnVisibility.createdAt}
          onCheckedChange={() => toggleColumn('createdAt')}
        >
          Created Date
        </DropdownMenuCheckboxItem>

        <DropdownMenuCheckboxItem
          checked={columnVisibility.createdBy}
          onCheckedChange={() => toggleColumn('createdBy')}
        >
          Created By
        </DropdownMenuCheckboxItem>

        <DropdownMenuCheckboxItem
          checked={columnVisibility.updatedAt}
          onCheckedChange={() => toggleColumn('updatedAt')}
        >
          Updated Date
        </DropdownMenuCheckboxItem>

        <DropdownMenuCheckboxItem
          checked={columnVisibility.updatedBy}
          onCheckedChange={() => toggleColumn('updatedBy')}
        >
          Updated By
        </DropdownMenuCheckboxItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ColumnVisibilityDropdown;

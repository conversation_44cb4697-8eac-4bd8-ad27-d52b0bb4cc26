import React from 'react';
// Layout is now applied centrally in App.tsx, so no need to wrap this page.
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import { Users, KeyRound } from 'lucide-react'; // Icons for dashboard cards

const AdminDashboardPage: React.FC = () => {
  return (
    // Layout wrapper removed
      <div className="space-y-6 max-w-4xl mx-auto animate-slide-in">
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <p className="text-muted-foreground">Manage users and application settings.</p>

        <div className="grid gap-4 md:grid-cols-2">
          <Link to="/admin/users" className="block hover:no-underline">
            <Card className="hover:bg-muted/50 transition-colors">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">User Management</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Manage Users</div>
                <p className="text-xs text-muted-foreground">
                  Register new users and view/delete existing accounts.
                </p>
              </CardContent>
            </Card>
          </Link>

          <Link to="/admin/password-reset" className="block hover:no-underline">
            <Card className="hover:bg-muted/50 transition-colors">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Password Reset</CardTitle>
                <KeyRound className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Reset Passwords</div>
                <p className="text-xs text-muted-foreground">
                  Reset passwords for existing user accounts.
                </p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    // Layout wrapper removed
  );
};

export default AdminDashboardPage;
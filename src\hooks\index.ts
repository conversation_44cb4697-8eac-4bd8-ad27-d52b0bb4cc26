/**
 * Hooks Index
 * 
 * This file exports all custom hooks from the hooks directory,
 * allowing for cleaner imports in components:
 * 
 * Example usage:
 * import { useLocalStorage, useDebounce } from '@/hooks';
 */

// Storage hooks
export * from './useLocalStorage';
export * from './useSessionStorage';

// UI/UX hooks
export * from './useDebounce';
export * from './useScrollRestoration';
export * from './use-mobile';
export * from './use-toast';

// Form hooks
export * from './useFormValidation';

// Data hooks
export * from './useItemFiltering'; 
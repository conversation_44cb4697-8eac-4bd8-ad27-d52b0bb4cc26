import { useState } from 'react';
import { toast } from './use-toast';

type StorageType = 'local' | 'session';

/**
 * Custom hook for managing web storage (localStorage or sessionStorage) data with React state
 *
 * @param {StorageType} storageType - 'local' for localStorage, 'session' for sessionStorage
 * @param {string} key - The key under which to store the value
 * @param {T} initialValue - The initial value to use if no value is found in storage
 * @returns {[T, (value: T | ((val: T) => T)) => void]} A stateful value and a function to update it
 * @template T - The type of the stored value
 */
export function useWebStorage<T>(storageType: StorageType, key: string, initialValue: T) {
  const storage = storageType === 'local' ? window.localStorage : window.sessionStorage;
  const storageName = storageType === 'local' ? 'local' : 'session';

  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      // Get from storage by key
      const item = storage.getItem(key);
      // Parse stored json or if none return initialValue
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // If error, show toast and return initialValue
      toast({
        title: "Storage Error",
        description: `Could not read from ${storageName} storage: ${(error as Error).message}`,
        variant: "destructive",
      });
      return initialValue;
    }
  });

  /**
   * Updates the storage value and state
   *
   * @param {T | ((val: T) => T)} value - New value or function to update existing value
   */
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      // Save state
      setStoredValue(valueToStore);
      // Save to storage
      storage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      // If error, log to console and show toast
      console.error(`Error writing to ${storageName}Storage key "${key}":`, error);
      toast({
        title: "Storage Error",
        description: `Could not save to ${storageName} storage: ${(error as Error).message}`,
        variant: "destructive",
      });
    }
  };

  return [storedValue, setValue] as const;
}
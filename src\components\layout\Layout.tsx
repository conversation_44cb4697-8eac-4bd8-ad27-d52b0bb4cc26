import React, { useMemo, useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { AppSidebar } from './app-sidebar';
import { Moon, Sun } from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger
} from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { useInventory } from '@/context/InventoryContext';
import { format } from 'date-fns';

interface LayoutProps {
  children: React.ReactNode;
}

// Maps route paths to human-readable breadcrumb titles for navigation.
const routeTitles: Record<string, string> = {
  // Dashboard
  '/': 'Dashboard',

  // Inventory
  '/items': 'All Items',
  '/add-item': 'Add Item',
  '/batch-add-items': 'Batch Add Items',
  '/batch-edit-items': 'Batch Edit Items',

  // Locations
  '/locations': 'Manage Locations',
  '/add-location': 'Add Location',

  // Settings
  '/settings': 'Settings',

  // Admin
  '/admin': 'Admin Dashboard',
};

// Maps settings tab keys to display names for breadcrumbs.
const settingsTabTitles: Record<string, string> = {
  'general': 'General',
  'preferences': 'Preferences',
  'profile': 'Profile',
  'data': 'Data',
  'admin': 'Admin'
};

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { pathname, search } = useLocation();
  const { theme, setTheme } = useTheme();
  const { items, getLocationById } = useInventory();

  // Tracks the current date and time for display in the header.
  const [currentDateTime, setCurrentDateTime] = useState(new Date());

  // Update the currentDateTime state every second to keep the displayed time accurate.
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Dynamically generates breadcrumb segments based on the current route and URL parameters.
  const generateBreadcrumbs = () => {
    const paths = pathname.split('/').filter(Boolean);

    // Special case: if on the root path, show only the dashboard breadcrumb.
    if (pathname === '/') {
      return [{ path: '/', label: 'Dashboard' }];
    }

    const breadcrumbs = [];
    let currentPath = '';

    // Always include the dashboard as the root breadcrumb.
    breadcrumbs.push({ path: '/', label: 'Dashboard' });

    // Build path segments
    for (let i = 0; i < paths.length; i++) {
      const path = paths[i];
      currentPath += `/${path}`;

      // Special case: handle settings routes with tab query parameters.
      if (path === 'settings') {
        breadcrumbs.push({ path: '/settings', label: 'Settings' });

        // If a tab is active in the query string, add it as a breadcrumb.
        const searchParams = new URLSearchParams(search);
        const activeTab = searchParams.get('tab');

        if (activeTab && settingsTabTitles[activeTab]) {
          breadcrumbs.push({
            path: `/settings?tab=${activeTab}`,
            label: settingsTabTitles[activeTab]
          });
        }

        return breadcrumbs;
      }

      // Special case: if viewing an item detail, use item name if available.
      if (paths.length >= 2 && paths[0] === 'items' && i === 1) {
        const itemId = path;
        const item = items.find(item => item.id === itemId);

        if (item) {
          // Use the item's name for the breadcrumb if found.
          breadcrumbs.push({ path: currentPath, label: item.name });
        } else {
          // If item is not found, fallback to showing the ID.
          breadcrumbs.push({ path: currentPath, label: path });
        }
        continue; // Skip the default handling
      }

      // Special case: if viewing a location detail, use location name if available.
      if (paths.length >= 2 && paths[0] === 'locations' && i === 1) {
        const locationId = path;
        const location = getLocationById(locationId);

        if (location) {
          // Use the location's name for the breadcrumb if found.
          breadcrumbs.push({ path: currentPath, label: location.name });
        } else {
          // If location is not found, fallback to showing the ID.
          breadcrumbs.push({ path: currentPath, label: path });
        }
        continue; // Skip the default handling
      }

      // Special case: handle edit-item route by showing the item name and 'Edit'.
      if (paths.length >= 2 && paths[0] === 'edit-item' && i === 1) {
        const itemId = path;
        const item = items.find(item => item.id === itemId);

        if (item) {
          breadcrumbs.push({ path: `/items/${itemId}`, label: item.name }); // Add breadcrumb for the item detail link.
          breadcrumbs.push({ path: currentPath, label: 'Edit' });
        } else {
          breadcrumbs.push({ path: currentPath, label: path });
        }
        continue; // Skip the default handling
      }

      // Special case: handle edit-location route by showing the location name and 'Edit'.
      if (paths.length >= 3 && paths[0] === 'locations' && paths[1] === 'edit' && i === 2) {
        const locationId = path;
        const location = getLocationById(locationId);

        if (location) {
          breadcrumbs.push({ path: `/locations/${locationId}`, label: location.name }); // Add breadcrumb for the location detail link.
          breadcrumbs.push({ path: currentPath, label: 'Edit' });
        } else {
          breadcrumbs.push({ path: currentPath, label: path });
        }
        continue; // Skip the default handling
      }

      // For all other paths, use the routeTitles mapping or capitalize the path segment.
      const label = routeTitles[currentPath] || path.charAt(0).toUpperCase() + path.slice(1);
      breadcrumbs.push({ path: currentPath, label });
    }

    return breadcrumbs;
  };

  // Memoize breadcrumbs to avoid unnecessary recalculation unless dependencies change.
  const breadcrumbs = useMemo(() => generateBreadcrumbs(), [pathname, search, items, getLocationById]);

  // Switch between light and dark themes when the theme toggle button is clicked.
  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className='border border-border/50'>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b border-border/50 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="flex-1">
            <BreadcrumbList>
              {breadcrumbs.map((crumb, index) => {
                const isLast = index === breadcrumbs.length - 1;

                return (
                  <React.Fragment key={crumb.path}>
                    <BreadcrumbItem className={isLast ? undefined : "hidden md:flex"}>
                      {isLast ? (
                        <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink asChild className="hover:text-primary">
                          <Link to={crumb.path}>{crumb.label}</Link>
                        </BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                    {!isLast && <BreadcrumbSeparator className="hidden md:flex" />}
                  </React.Fragment>
                );
              })}
            </BreadcrumbList>
          </Breadcrumb>
          <div className="ml-auto flex items-center gap-4">
            <div className="text-xs text-muted-foreground hidden md:block">
              {format(currentDateTime, 'PPP')} {format(currentDateTime, 'p')}
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={toggleTheme}
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </Button>
          </div>
        </header>
        <main className="flex-1 p-6 pt-4">{children}</main>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default Layout;

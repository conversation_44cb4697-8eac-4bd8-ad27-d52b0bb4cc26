import { Item, Location } from '@/types';
import { getUTCDate } from '@/utils/dateUtils'; // Assuming getUTCDate is exported or accessible

// Provides initial sample data for development and fallback scenarios.
// These samples help developers test and demo the app without requiring manual data entry.

export const initialLocationsData: Location[] = [
  {
    id: "loc-1",
    name: "Sample Location",
    description: "Location description",
    color: "#888888",
    createdAt: getUTCDate(), // Use consistent UTC date formatting
    updatedAt: getUTCDate()
  }
];

export const initialItemsData: Item[] = [
  {
    name: "Sample Item",
    quantity: 5,
    unit: "pieces",
    locationId: "loc-1", // Must match an ID in initialLocationsData for referential integrity
    description: "Description of the item.",
    expiryDate: "2027-03-12", // Keep as string YYYY-MM-DD
    watchStock: false,
    id: "item-1",
    createdAt: getUTCDate(), // Use consistent UTC date formatting
    updatedAt: getUTCDate()
  }
];
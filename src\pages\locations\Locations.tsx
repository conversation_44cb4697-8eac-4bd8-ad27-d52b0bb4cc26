import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Warehouse, Plus, Search, Edit, Trash2, X, Package, ArrowRight } from 'lucide-react';
import EmptyState from '@/components/ui/EmptyState';
import { useInventory } from '@/context/InventoryContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { FloatingActionButton } from '@/components/ui/FloatingActionButton';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

const Locations = () => {
  const { locations, deleteLocation, getItemsInLocation } = useInventory();
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();

  const filteredLocations = locations.filter(
    location => location.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    // Locations page root
    <>
      <div className="space-y-6 animate-slide-in">
        {/* Page header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Warehouse className="h-7 w-7 text-foreground" />
              Locations
            </h1>
            <p className="text-muted-foreground">
              Manage your inventory storage locations.
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => navigate('/add-location')} className="hidden md:flex">
              <Plus className="mr-2 h-4 w-4" />
              Add Location
            </Button>
          </div>
        </div>

        {/* Search bar */}
        <div className="relative w-full md:max-w-sm">
  <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
  <Input
    type="text"
    placeholder="Search locations..."
    className="pl-8 pr-10"
    value={searchTerm}
    onChange={(e) => setSearchTerm(e.target.value)}
  />
  {searchTerm && (
    <button
      type="button"
      className="absolute right-2.5 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
      onClick={() => setSearchTerm('')}
      tabIndex={0}
      aria-label="Clear search"
    >
      <X className="h-4 w-4" />
    </button>
  )}
</div>

        {/* Locations grid or empty state */}
        <div className="mb-14 md:mb-0">
          {filteredLocations.length === 0 ? (
            <EmptyState
              title={locations.length === 0 ? "No locations found" : "No results found"}
              description={
                locations.length === 0
                  ? "You don't have any locations yet."
                  : "Try adjusting your search settings."
              }
              icon={
                locations.length === 0
                  ? <Warehouse className="h-6 w-6 text-muted-foreground" />
                  : <Search className="h-6 w-6 text-muted-foreground" />
              }
              buttonText={locations.length === 0 ? "Add First Location" : undefined}
              buttonAction={locations.length === 0 ? () => navigate('/add-location') : undefined}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredLocations.map(location => {
                const itemsInLocation = getItemsInLocation(location.id);
                const itemCount = itemsInLocation.length;
                const totalCount = itemsInLocation.reduce((sum, item) => sum + item.quantity, 0);

                return (
                  <Card key={location.id} className="w-full overflow-hidden gap-0 pt-3">

                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-9 rounded-md flex items-center justify-center"
                            style={{ backgroundColor: location.color || '#888888' }}
                          >
                          </div>
                          <div>
                            <CardTitle className="text-md font-semibold truncate">{location.name}</CardTitle>
                            {location.description && (
                              <CardDescription className="line-clamp-1 text-sm">{location.description}</CardDescription>
                            )}
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => navigate(`/locations/edit/${location.id}`)}
                            className="h-8 w-8"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>

                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <span className="inline-block">
                                {itemCount > 0 ? (
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <span>
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          disabled
                                          className="h-8 w-8 text-muted-foreground hover:text-destructive"
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      </span>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      Cannot delete a location with items
                                    </TooltipContent>
                                  </Tooltip>
                                ) : (
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 text-muted-foreground hover:text-destructive"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                )}
                              </span>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  This will permanently delete the location "{location.name}".
                                  This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => deleteLocation(location.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1">
                          <Package className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">{itemCount}</span>
                          <span className="text-sm text-muted-foreground">{itemCount === 1 ? 'item' : 'items'}</span>
                        </div>
                        <div className="text-sm text-muted-foreground">{totalCount} total units</div>
                      </div>
                    </CardContent>
                    <CardFooter className="pt-0">
                      <Button
                        variant="outline"
                        className="w-full justify-between hover:bg-muted/50 h-9"
                        onClick={() => navigate(`/locations/${location.id}`)}
                      >
                        <span>View Items</span>
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Mobile floating action button */}
      <FloatingActionButton
        actions={[
          {
            icon: <Warehouse className="h-5 w-5" />,
            label: "New Location",
            onClick: () => navigate('/add-location')
          }
        ]}
      />
    </>
  );
};

export default Locations;

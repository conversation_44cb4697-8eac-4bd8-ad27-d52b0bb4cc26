import { useWebStorage } from './useWebStorage';

/**
 * Custom hook for managing session storage data with React state
 * (Thin wrapper around useWebStorage)
 *
 * @param {string} key - The key under which to store the value in sessionStorage
 * @param {T} initialValue - The initial value to use if no value is found in sessionStorage
 * @returns {[T, (value: T | ((val: T) => T)) => void]} A stateful value and a function to update it
 * @template T - The type of the stored value
 */
export function useSessionStorage<T>(key: string, initialValue: T) {
  return useWebStorage<T>('session', key, initialValue);
}
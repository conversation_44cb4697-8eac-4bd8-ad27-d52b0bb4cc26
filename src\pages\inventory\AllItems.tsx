import { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Search, Package, Plus, Trash2, MoveRight, Loader2, X, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FloatingActionButton } from '@/components/ui/FloatingActionButton';
import DataTableFilter from '@/components/items-table/DataTableFilter';
import DataTable from '@/components/items-table/DataTable';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import ShowLimitSelect from '@/components/items-table/ShowLimitSelect';
import LocationColorSwatch from '@/components/items-table/LocationColorSwatch';
import { useInventory } from '@/context/InventoryContext';
import { toast } from "sonner";
import { useDebounce } from '@/hooks/useDebounce';
import { useScrollRestoration } from '@/hooks/useScrollRestoration';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { useItemFiltering } from '@/hooks/useItemFiltering'; 

const EXPIRY_FILTER_OPTIONS = [
  { label: 'Clear filter', value: 'none' },
  { label: 'Already expired', value: 'expired' },
  { label: '30 days', value: '30' },
  { label: '60 days', value: '60' },
  { label: '90 days', value: '90' },
];

const AllItems = () => {
  const { items, locations, getLocationById, deleteItem, updateItem, isLoading } = useInventory();
  const navigate = useNavigate();

  // Search state
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300); // Uncomment debounce
  const [isSearching, setIsSearching] = useState(false);

  // Persistent user preferences
  const [sortField, setSortField] = useLocalStorage<'name' | 'quantity' | 'updatedAt' | 'expiryDate'>('allItems.sortField', 'name');
  const [sortDirection, setSortDirection] = useLocalStorage<'asc' | 'desc'>('allItems.sortDirection', 'asc');
  const [filterLocation, setFilterLocation] = useLocalStorage<string>('allItems.filterLocation', 'all');
  const [itemsPerPage, setItemsPerPage] = useLocalStorage<number>('allItems.itemsPerPage', 10);
  const [expiryFilter, setExpiryFilter] = useLocalStorage<string>('allItems.expiryFilter', 'none');
  const [filterLowStock, setFilterLowStock] = useState(false); // Add state for low stock filter
  const [currentPage, setCurrentPage] = useState(1);

  // const [columnVisibility, setColumnVisibility] = useLocalStorage<ColumnVisibility>('allItems.columnVisibility', {
  //   name: true,
  //   quantity: true,
  //   location: true,
  //   expiryDate: true,
  //   description: true,
  //   createdAt: false,
  //   updatedAt: false,
  //   createdBy: false,
  //   updatedBy: false,
  // });

  // Selection and dialog state
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showMoveDialog, setShowMoveDialog] = useState(false);
  const [targetLocationId, setTargetLocationId] = useState<string>("");
  const [itemToDelete, setItemToDelete] = useState<string | null>(null);

  // Scroll restoration & Location state handling
  const location = useLocation();
  useScrollRestoration('allItems');

  useEffect(() => {
    const incomingFilter = location.state?.filter;
    if (incomingFilter) {
      // Reset current page to 1 when applying a new filter
      setCurrentPage(1);

      if (incomingFilter === 'expired') {
        // For expired items
        setExpiryFilter('expired');
        setFilterLowStock(false); // Clear other filters
        setFilterLocation('all'); // Reset location filter
      } else if (incomingFilter === 'expiring') {
        // For expiring items - use the user's configured threshold
        const defaultExpiringDays = localStorage.getItem('expiring-threshold') || '30';
        setExpiryFilter(defaultExpiringDays);
        setFilterLowStock(false); // Clear other filters
        setFilterLocation('all'); // Reset location filter
      } else if (incomingFilter === 'low-stock') {
        // For low stock items
        setFilterLowStock(true);
        setExpiryFilter('none'); // Clear expiry filter
        setFilterLocation('all'); // Reset location filter
      }

      // Clear the state after applying it to prevent reapplying on navigation
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location.state, location.pathname, setExpiryFilter, setFilterLowStock, setFilterLocation, setCurrentPage, navigate]);

  // Search animation effect
  useEffect(() => {
    setIsSearching(!!searchTerm);
    const timer = setTimeout(() => setIsSearching(false), 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Get low stock threshold (consider moving to a settings context later)
  const lowStockThreshold = useMemo(() =>
    localStorage.getItem('low-stock-threshold') ? parseInt(localStorage.getItem('low-stock-threshold')!) : 3
  , []);

  // Use the custom hook for filtering and sorting
  const processedItems = useItemFiltering({
    items,
    locations,
    searchTerm: debouncedSearchTerm, // Use debounced term
    sortField: sortField as keyof typeof items[0] | 'locationName', // Proper type instead of any
    sortDirection,
    filterLocation,
    expiryFilter,
    filterLowStock,
    lowStockThreshold,
  });

  // Pagination calculations
  const { startIndex, totalPages } = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const totalPages = Math.ceil(processedItems.length / itemsPerPage);
    return { startIndex, totalPages };
  }, [currentPage, itemsPerPage, processedItems.length]);

  // Current page items
  const paginatedItems = useMemo(() =>
    processedItems.slice(startIndex, startIndex + itemsPerPage),
    [processedItems, startIndex, itemsPerPage]);

  // Selection handlers
  const handleSelectItem = useCallback((itemId: string) => {
    setSelectedItems(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(itemId)) {
        newSelected.delete(itemId);
      } else {
        newSelected.add(itemId);
      }
      return newSelected;
    });
  }, []);

  const handleSelectAll = useCallback(() => {
    setSelectedItems(prev =>
      prev.size === paginatedItems.length
        ? new Set()
        : new Set(paginatedItems.map(item => item.id))
    );
  }, [paginatedItems]);

  // Sorting handler
  const toggleSort = useCallback((field: string) => {
    setSortDirection(prev =>
      field === sortField ? (prev === 'asc' ? 'desc' : 'asc') : 'asc'
    );
    setSortField(field as 'name' | 'quantity' | 'updatedAt' | 'expiryDate');
  }, [sortField, setSortField, setSortDirection]);

  // Delete handlers
  const handleDelete = useCallback((itemId: string) => {
    setItemToDelete(itemId);
    setShowDeleteConfirm(true);
  }, []);

  const handleConfirmDelete = useCallback(() => {
    if (itemToDelete) {
      deleteItem(itemToDelete);
      setItemToDelete(null);
    }
    setShowDeleteConfirm(false);
  }, [itemToDelete, deleteItem]);

  const handleBatchDelete = useCallback(() => {
    const count = selectedItems.size;
    Array.from(selectedItems).forEach(itemId => deleteItem(itemId));
    setSelectedItems(new Set());
    setShowDeleteConfirm(false);
    toast.success("Items deleted", {
      description: `Successfully deleted ${count} items.`
    });
  }, [selectedItems, deleteItem]);

  // Move items handler
  const handleBatchMove = useCallback(() => {
    if (!targetLocationId) return;

    const count = selectedItems.size;
    const locationName = locations.find(l => l.id === targetLocationId)?.name;

    Array.from(selectedItems).forEach(itemId => {
      const item = items.find(i => i.id === itemId);
      if (item) {
        updateItem(itemId, { ...item, locationId: targetLocationId });
      }
    });

    setSelectedItems(new Set());
    setShowMoveDialog(false);

    toast.success("Items moved", {
      description: `Successfully moved ${count} items to ${locationName}.`
    });
  }, [selectedItems, targetLocationId, locations, items, updateItem]);

  return (
    // Return a single root element
    <>
      <div className="space-y-6 animate-slide-in">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Package className="h-7 w-7 text-primary" />
              All Items
            </h1>
            <p className="text-muted-foreground">
              Browse and manage your inventory items.
            </p>
          </div>
          <Button onClick={() => navigate('/add-item')} className="hidden md:flex">
            <Plus className="mr-2 h-4 w-4" />
            Add Item
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-4">
          {/* Search */}
          <div className="relative md:flex-1 md:max-w-md">
            {isSearching ? (
              <Loader2 className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground animate-spin" />
            ) : (
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            )}
            <Input
              type="text"
              placeholder="Search items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-10 w-full"
              disabled={isLoading}
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 h-6 w-6 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                onClick={() => setSearchTerm('')}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Clear search</span>
              </Button>
            )}
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 md:flex md:items-center md:gap-2 md:flex-shrink-0">
            {/* Column visibility feature removed */}
            <DataTableFilter
              value={filterLocation || 'all'}
              onValueChange={setFilterLocation}
              options={[
                { label: 'All locations', value: 'all' },
                ...locations.map(location => ({
                  label: (
                    <div className="flex items-center gap-2">
                      <LocationColorSwatch color={location.color} />
                      <span>{location.name}</span>
                    </div>
                  ),
                  value: location.id
                }))
              ]}
              placeholder="Filter by location"
              disabled={isLoading}
              className="w-full md:w-[180px]"
            />

            <DataTableFilter
              value={expiryFilter}
              onValueChange={setExpiryFilter}
              options={EXPIRY_FILTER_OPTIONS}
              placeholder="Filter by expiry"
              disabled={isLoading}
              className="w-full md:w-[160px]"
            />

            <ShowLimitSelect
              value={itemsPerPage}
              onChange={(value) => {
                setItemsPerPage(value);
                setCurrentPage(1);
              }}
              totalItems={processedItems.length}
              disabled={isLoading}
              className="w-full md:w-auto"
            />
          </div>
        </div>

        {/* Batch Actions */}
        {selectedItems.size > 0 && (
          <div className="bg-muted/50 border border-border rounded-md p-3 mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">
                {selectedItems.size} items selected
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-muted-foreground"
                onClick={() => setSelectedItems(new Set())}
              >
                Clear
              </Button>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDeleteConfirm(true)}
                className="text-destructive hover:text-destructive w-full"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowMoveDialog(true)}
                className="w-full"
              >
                <MoveRight className="h-4 w-4 mr-2" />
                Move
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate('/batch-edit-items', { state: { selectedItems: Array.from(selectedItems) } })}
                className="w-full"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>
        )}

        {/* Data Table */}
        <div className="mb-14 md:mb-0">
          <DataTable
            items={items}
            locations={locations}
            getLocationById={getLocationById}
            isLoading={isLoading}
            paginatedItems={paginatedItems}
            sortField={sortField}
            sortDirection={sortDirection}
            itemsPerPage={itemsPerPage}
            currentPage={currentPage}
            totalPages={totalPages}
            startIndex={startIndex}
            sortedItems={processedItems}
            selectedItems={selectedItems}
            showLocationColumn={true}
            emptyMessage={{
              title:
                items.length === 0
                  ? "No items found"
                  : "No results found",
              description:
                items.length === 0
                  ? "You don't have any items in your inventory yet."
                  : "Try adjusting your search or filter settings.",
              buttonText: items.length === 0 ? "Add First Item" : undefined,
              buttonAction: items.length === 0 ? () => navigate('/add-item') : undefined,
              icon: items.length === 0
                ? <Package className="h-6 w-6 text-muted-foreground" />
                : <Search className="h-6 w-6 text-muted-foreground" />
            }}
            onToggleSort={toggleSort}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={setItemsPerPage}
            onSelectItem={handleSelectItem}
            onSelectAll={handleSelectAll}
            onEdit={(id) => navigate(`/edit-item/${id}`)}
            onDelete={handleDelete}
          />
        </div>
      </div>

      {/* Mobile Add Button */}
      <FloatingActionButton
        actions={[
          {
            icon: <Plus className="h-5 w-5" />,
            label: "Add Item",
            onClick: () => navigate('/add-item')
          }
        ]}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              {itemToDelete
                ? "This will permanently delete this item."
                : `This will permanently delete ${selectedItems.size} selected items.`}
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={itemToDelete ? handleConfirmDelete : handleBatchDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Move Items Dialog */}
      <Dialog open={showMoveDialog} onOpenChange={setShowMoveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Move Items</DialogTitle>
            <DialogDescription>
              Select a location to move {selectedItems.size} items to.
            </DialogDescription>
          </DialogHeader>
          <Select value={targetLocationId} onValueChange={setTargetLocationId}>
            <SelectTrigger>
              <SelectValue placeholder="Select a location" />
            </SelectTrigger>
            <SelectContent>
              {locations.map(location => (
                <SelectItem key={location.id} value={location.id}>
                  <div className="flex items-center gap-2">
                    <LocationColorSwatch color={location.color} />
                    <span>{location.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowMoveDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleBatchMove} disabled={!targetLocationId}>
              Move Items
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AllItems;

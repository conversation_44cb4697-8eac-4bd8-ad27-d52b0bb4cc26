import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

const AdminRoute: React.FC = () => {
  const { user, isLoading, isAuthenticated } = useAuth();

  if (isLoading) {
    // Optional: Show a loading indicator while auth state is resolving
    return <div>Loading Admin Access...</div>;
  }

  if (!isAuthenticated || user?.role !== 'admin') {
    // Redirect non-admins or unauthenticated users to the home page
    // Or show an "Unauthorized" page
    return <Navigate to="/" replace />;
  }

  // If authenticated and user is an admin, render the child routes
  return <Outlet />;
};

export default AdminRoute;
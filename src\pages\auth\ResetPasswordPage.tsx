import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { BookHeart, ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils'; // Utility for conditional class name merging
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';

/**
 * ResetPasswordPage Component
 *
 * Renders the password reset form for users with a valid token. Handles new password input,
 * validation, submission, feedback, and redirects to login on success.
 */
const ResetPasswordPage: React.FC = () => {
  const { resetPasswordWithToken } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // --- State ---
  // Local state for form fields, feedback, and submission status
  const [token, setToken] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInvalid, setIsInvalid] = useState(false);

  // --- Effects ---
  // Extract token from URL query parameters
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tokenParam = searchParams.get('token');
    if (tokenParam) {
      setToken(tokenParam);
    } else {
      setError("No reset token found in URL. Please request a new password reset link.");
      setIsInvalid(true);
    }
  }, [location.search]);

  // --- Handlers ---
  // Handle password input change and clear error state if needed
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (isInvalid) {
      setIsInvalid(false);
      setError(null);
    }
  };

  // Handle confirm password input change and clear error state if needed
  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(e.target.value);
    if (isInvalid) {
      setIsInvalid(false);
      setError(null);
    }
  };

  /**
   * Handles password reset form submission:
   * - Validates all fields (token, password, confirmPassword)
   * - Calls resetPasswordWithToken and handles result
   * - Shows toasts and redirects on success
   */
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset error state
    setError(null);
    setIsInvalid(false);
    
    // Validate inputs
    if (!token) {
      setError("Reset token is missing");
      setIsInvalid(true);
      return;
    }
    
    if (!password) {
      setError("Password is required");
      setIsInvalid(true);
      return;
    }
    
    if (password.length < 6) {
      setError("Password must be at least 6 characters");
      setIsInvalid(true);
      return;
    }
    
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setIsInvalid(true);
      return;
    }

    setIsSubmitting(true);
    
    try {
      const success = await resetPasswordWithToken(token, password);

      if (success) {
        // Password reset successful - redirect to login page
        toast.success("Password Reset Successful", {
          description: "Your password has been reset. You can now log in with your new password."
        });
        navigate('/login');
      } else {
        // Reset failed (handled by the auth function with toast)
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error("Password reset error:", error);
      setError("An unexpected error occurred. Please try again.");
      setIsInvalid(true);
      setIsSubmitting(false);
    }
  }, [token, password, confirmPassword, resetPasswordWithToken, navigate]);

  // --- Rendering ---
  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-md space-y-8">
        {/* App Title and Description Section */}
        <div className="flex flex-col items-center text-center">
          <BookHeart className="h-12 w-12 text-primary" />
          <h1 className="mt-2 text-2xl font-bold">Imbentorys</h1>
          <p className="text-sm text-muted-foreground">Reset your password</p>
        </div>

        {/* Password Reset Form Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Reset Password</CardTitle>
            <CardDescription>Create a new password for your account</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password">New Password*</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter new password (min. 6 characters)"
                  value={password}
                  onChange={handlePasswordChange}
                  required
                  aria-invalid={isInvalid}
                  className={cn(isInvalid && "border-destructive focus-visible:ring-destructive")}
                  disabled={!token}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password*</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="Confirm your new password"
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  required
                  aria-invalid={isInvalid}
                  className={cn(isInvalid && "border-destructive focus-visible:ring-destructive")}
                  disabled={!token}
                />
              </div>
              {/* Error message for invalid reset attempts */}
              {error && (
                <div className="bg-destructive/10 text-destructive rounded-md p-3 text-sm">
                  {error}
                </div>
              )}
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isSubmitting || !token}
              >
                {isSubmitting ? 'Resetting Password...' : 'Reset Password'}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Link to="/login" className="text-primary hover:underline flex items-center gap-1">
              <ArrowLeft className="h-4 w-4" />
              Back to Login
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ResetPasswordPage;

// Rebuild script for Imbentory application
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🔄 Rebuilding Imbentory application...');

try {
  // Clean up previous build
  console.log('🧹 Cleaning up previous build...');
  if (fs.existsSync('./dist')) {
    fs.rmSync('./dist', { recursive: true, force: true });
  }

  // Install dependencies if needed
  if (!fs.existsSync('./node_modules')) {
    console.log('📦 Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
  }

  // Build the application
  console.log('🏗️ Building application...');
  execSync('npm run build', { stdio: 'inherit' });

  // Copy service worker to dist directory
  console.log('📋 Copying service worker to dist directory...');
  if (fs.existsSync('./public/background.js')) {
    fs.copyFileSync('./public/background.js', './dist/background.js');
  } else {
    console.log('⚠️ Warning: background.js not found in public directory, skipping copy.');
  }

  console.log('✅ Build completed successfully!');
  console.log('You can now run the application with \'npm run dev\' or \'npm run preview\'');
} catch (error) {
  console.error('❌ Error during rebuild:', error.message);
  process.exit(1);
}

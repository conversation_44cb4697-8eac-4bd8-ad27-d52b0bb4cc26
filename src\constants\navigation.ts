import { Home, Package, Warehouse, Settings, LogIn } from 'lucide-react';

export interface NavItem {
  path: string;
  label: string;
  icon: React.ElementType; // React component for the navigation icon
  authRequired?: boolean; // If true, link is only shown to authenticated users
  adminRequired?: boolean; // If true, link is only shown to admins
  hideWhenAuth?: boolean; // If true, link is hidden when user is authenticated
}

export const mainNavItems: NavItem[] = [
  { path: '/', label: 'Dashboard', icon: Home, authRequired: true },
  { path: '/items', label: 'All Items', icon: Package, authRequired: true },
  { path: '/locations', label: 'Manage Locations', icon: Warehouse, authRequired: true },
  { path: '/settings', label: 'Settings', icon: Settings, authRequired: true },
];

export const authNavItems: NavItem[] = [
   // Profile link will be handled separately within Layout for showing username
  { path: '/login', label: 'Login', icon: LogIn, hideWhenAuth: true },
  // Logout is an action, not a link, handled separately in Layout
];

// Navigation arrays can be combined or kept separate for flexible rendering in Layout.tsx
import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App.tsx';
import { AuthProvider } from './context/AuthContext.tsx';
import './index.css';

import './utils/storageUtils';
import { ensureAdminExists } from './utils/adminUtils';
import { migrateUsers } from './utils/migrations';
// Run storage migration to fix/correct localStorage keys before app loads
import { runStorageMigration } from './utils/storageMigration';
runStorageMigration();

// Ensure an admin account exists if none
ensureAdminExists();

// Run user data migrations
migrateUsers().catch(error => {
  console.error('Migration failed:', error);
});



// React Router v7 future flags
const future = {
  v7_startTransition: true,
  v7_relativeSplatPath: true,
};

createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter future={future}>
      <AuthProvider>
        <App />
      </AuthProvider>
    </BrowserRouter>
  </React.StrictMode>
);

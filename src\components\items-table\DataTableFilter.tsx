import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface FilterOption {
  label: React.ReactNode;
  value: string;
}

interface DataTableFilterProps {
  value: string;
  onValueChange: (value: string) => void;
  options: FilterOption[];
  placeholder: string;
  disabled?: boolean;
  className?: string;
}

const DataTableFilter = React.memo(({
  value,
  onValueChange,
  options,
  placeholder,
  disabled = false,
  className = 'w-[200px]'
}: DataTableFilterProps) => {
  return (
    <Select 
      value={value} 
      onValueChange={onValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent className="max-h-[200px]">
        {options.map(option => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
});

DataTableFilter.displayName = 'DataTableFilter';

export default DataTableFilter; 
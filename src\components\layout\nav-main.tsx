import { Link, useLocation, useNavigate } from 'react-router-dom';
import { mainNavItems } from '@/constants/navigation';
import { cn } from '@/lib/utils';

export function NavMain() {
  const { pathname } = useLocation();
  const navigate = useNavigate();

  // Handles navigation clicks. If 'All Items' is clicked, resets filters and navigates with a clean state.
  const handleNavClick = (event: React.MouseEvent, path: string, label: string) => {
    // If clicking on All Items, reset the filters
    if (path === '/items' && label === 'All Items') {
      // Reset all item filters in localStorage to their default values.
      localStorage.setItem('allItems.expiryFilter', 'none');
      localStorage.setItem('allItems.filterLocation', 'all');
      localStorage.setItem('allItems.sortField', 'name');
      localStorage.setItem('allItems.sortDirection', 'asc');

      // Ensure navigation uses a clean URL, removing any lingering query parameters.

      // Reset any other filters that could affect the item list.
      // Prevent default Link behavior so navigation and filter reset work together.
      event.preventDefault();

      // Use programmatic navigation to guarantee all filters are reset.
      navigate(path);
    }
  };

  return (
    <div className="px-4 py-2">
      <h3 className="mb-2 text-xs font-medium text-muted-foreground">Main Navigation</h3>
      <nav className="grid gap-1">
        {mainNavItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            onClick={(e) => handleNavClick(e, item.path, item.label)}
            className={cn(
              "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
              pathname === item.path ? "bg-accent text-accent-foreground" : "transparent"
            )}
          >
            <item.icon className="h-4 w-4" />
            {item.label}
          </Link>
        ))}
      </nav>
    </div>
  );
}
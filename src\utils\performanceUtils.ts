import { toast } from "@/components/ui/use-toast";

// Constants for performance thresholds
const PERFORMANCE_THRESHOLDS = {
  RENDER_TIME: 16.67, // 60fps target (1000ms / 60) - time to render a frame
  FILTER_TIME: 50,   // Maximum time in milliseconds for filtering operations
  SORT_TIME: 50,     // Maximum time in milliseconds for sorting operations
  STORAGE_TIME: 100  // Maximum time in milliseconds for storage operations
} as const;

// Performance measurement utility
export const measurePerformance = (operation: string, callback: () => void): number => {
  const start = performance.now();
  callback();
  const end = performance.now();
  const duration = end - start;

  return duration;
};

// Performance monitoring for React components
export const monitorComponentPerformance = (componentName: string, renderTime: number) => {
  if (renderTime > PERFORMANCE_THRESHOLDS.RENDER_TIME) {
    console.warn(
      `[Performance Warning] ${componentName} took ${renderTime.toFixed(2)}ms to render. ` +
      `Target: ${PERFORMANCE_THRESHOLDS.RENDER_TIME}ms`
    );
  }
};

// Monitor filtering performance
export const monitorFilterPerformance = (items: unknown[], filterTime: number) => {
  if (filterTime > PERFORMANCE_THRESHOLDS.FILTER_TIME) {
    console.warn(
      `[Performance Warning] Filtering ${items.length} items took ${filterTime.toFixed(2)}ms. ` +
      `Target: ${PERFORMANCE_THRESHOLDS.FILTER_TIME}ms`
    );

    if (items.length > 1000) {
      toast({
        title: "Performance Notice",
        description: "Large dataset detected. Consider using batch operations for better performance.",
        variant: "destructive"
      });
    }
  }
};

// Monitor sorting performance
export const monitorSortPerformance = (items: unknown[], sortTime: number) => {
  if (sortTime > PERFORMANCE_THRESHOLDS.SORT_TIME) {
    console.warn(
      `[Performance Warning] Sorting ${items.length} items took ${sortTime.toFixed(2)}ms. ` +
      `Target: ${PERFORMANCE_THRESHOLDS.SORT_TIME}ms`
    );
  }
};

// Monitor storage operations
export const monitorStoragePerformance = (operation: string, storageTime: number) => {
  if (storageTime > PERFORMANCE_THRESHOLDS.STORAGE_TIME) {
    console.warn(
      `[Performance Warning] Storage operation "${operation}" took ${storageTime.toFixed(2)}ms. ` +
      `Target: ${PERFORMANCE_THRESHOLDS.STORAGE_TIME}ms`
    );
  }
};

// Create a performance report
export const generatePerformanceReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    metrics: {
      memory: ('memory' in performance) ? {
        usedJSHeapSize: Math.round((performance as unknown as { memory: { usedJSHeapSize: number } }).memory.usedJSHeapSize / (1024 * 1024)),
        totalJSHeapSize: Math.round((performance as unknown as { memory: { totalJSHeapSize: number } }).memory.totalJSHeapSize / (1024 * 1024)),
        jsHeapSizeLimit: Math.round((performance as unknown as { memory: { jsHeapSizeLimit: number } }).memory.jsHeapSizeLimit / (1024 * 1024))
      } : null,
      navigation: performance.getEntriesByType('navigation')[0],
      resources: performance.getEntriesByType('resource'),
      marks: performance.getEntriesByType('mark'),
      measures: performance.getEntriesByType('measure')
    }
  };

  console.log('[Performance Report]', report);
  return report;
};

// Helper to mark important events
export const markPerformanceEvent = (eventName: string) => {
  performance.mark(eventName);
};

// Helper to measure between two marks
export const measurePerformanceBetweenMarks = (measureName: string, startMark: string, endMark: string) => {
  performance.measure(measureName, startMark, endMark);
  const measures = performance.getEntriesByName(measureName);
  return measures[measures.length - 1].duration;
};
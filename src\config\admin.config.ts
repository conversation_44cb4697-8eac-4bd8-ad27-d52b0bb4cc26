/**
 * admin.config.ts
 *
 * Configuration for the default admin account and first-launch behavior.
 * Used to bootstrap the system if no users exist. In production, these values
 * should be changed or replaced with environment variables for security.
 */

export const adminConfig = {
  // Default admin credentials
  username: 'admin',
  password: '124356!', // This should be a strong password
  fullName: 'Ren Cats',
  email: '<EMAIL>',
  
  // First launch behavior
  createOnFirstLaunch: true, // Set to false to disable automatic admin creation
  
  // Security settings
  forcePasswordChangeOnFirstLogin: true, // Not implemented yet, but a good idea
};

export default adminConfig;

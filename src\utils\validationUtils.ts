/**
 * Validation utility functions for form input and business logic.
 * Uses constants from the validation.ts for consistent validation rules.
 */

import { PATTERNS, MAX_NAME_LENGTH, MAX_DESCRIPTION_LENGTH, MAX_URL_LENGTH } from '@/constants/validation';

/**
 * Checks if a string is empty or only whitespace
 * @param value - The string to check
 * @returns True if the string is empty or only whitespace
 */
export const isEmpty = (value: string | null | undefined): boolean => {
  return value === null || value === undefined || value.trim() === '';
};

/**
 * Validates that a quantity is a positive number
 * @param value - The number to check
 * @returns True if the value is a valid non-negative number
 */
export const isValidQuantity = (value: number | null | undefined): boolean => {
  return value !== null && value !== undefined && !isNaN(value) && value >= 0;
};

/**
 * Validates that a name is within length limits
 * @param name - The name to validate
 * @returns True if the name is valid
 */
export const isValidName = (name: string | null | undefined): boolean => {
  if (isEmpty(name)) return false;
  return name!.length <= MAX_NAME_LENGTH;
};

/**
 * Validates that a description is within length limits
 * @param description - The description to validate
 * @returns True if the description is valid or empty
 */
export const isValidDescription = (description: string | null | undefined): boolean => {
  if (isEmpty(description)) return true; // Description is optional
  return description!.length <= MAX_DESCRIPTION_LENGTH;
};

/**
 * Validates a URL format
 * @param url - The URL to validate
 * @returns True if the URL is valid or empty
 */
export const isValidUrl = (url: string | null | undefined): boolean => {
  if (isEmpty(url)) return true; // URL is optional
  if (url!.length > MAX_URL_LENGTH) return false;
  return PATTERNS.URL.test(url!);
};

/**
 * Validates an email format
 * @param email - The email to validate
 * @returns True if the email is valid or empty
 */
export const isValidEmail = (email: string | null | undefined): boolean => {
  if (isEmpty(email)) return true; // Email is optional
  return PATTERNS.EMAIL.test(email!);
};

/**
 * Validates a date string format (YYYY-MM-DD)
 * @param date - The date string to validate
 * @returns True if the date is valid or empty
 */
export const isValidDateFormat = (date: string | null | undefined): boolean => {
  if (isEmpty(date)) return true; // Date is optional
  return PATTERNS.DATE.test(date!);
};

/**
 * Validates that a date is not in the future
 * @param dateStr - The date string to validate
 * @returns True if the date is valid and not in the future
 */
export const isNotFutureDate = (dateStr: string | null | undefined): boolean => {
  if (isEmpty(dateStr)) return true; // Date is optional
  if (!isValidDateFormat(dateStr)) return false;
  
  const date = new Date(dateStr!);
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Compare dates without time
  
  return date <= today;
};

/**
 * Validates that an input is a valid number
 * @param value - The value to check
 * @returns True if the value is a valid number or empty
 */
export const isValidNumber = (value: string | null | undefined): boolean => {
  if (isEmpty(value)) return true; // Number is optional
  return PATTERNS.NUMBER.test(value!);
};

/**
 * Validates that an input is a valid decimal number
 * @param value - The value to check
 * @returns True if the value is a valid decimal number or empty
 */
export const isValidDecimal = (value: string | null | undefined): boolean => {
  if (isEmpty(value)) return true; // Decimal is optional
  return PATTERNS.DECIMAL.test(value!);
}; 